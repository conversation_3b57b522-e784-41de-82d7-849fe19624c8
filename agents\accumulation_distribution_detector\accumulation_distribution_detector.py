#!/usr/bin/env python3
"""
Accumulation/Distribution Master Agent - FIXED
Gets data through Data Ingestion Agent like all other agents
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime, date
import warnings
import sys
from pathlib import Path

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from agents.agent_base import BaseAgent

warnings.filterwarnings('ignore')

@dataclass
class AccumulationDistributionConfig:
    """Configuration for accumulation/distribution detection"""
    timeout_ms: int = 5000
    precision_threshold: float = 0.001
    enable_validation: bool = True
    lookback_periods: int = 20
    confidence_threshold: float = 65.0
    volume_weight: float = 0.35
    price_weight: float = 0.40
    momentum_weight: float = 0.25


class AccumulationDistributionAgent(BaseAgent):
    """
    Accumulation/Distribution Master Agent
    Gets data through Data Ingestion Agent like all other agents
    """
    
    task_id = "ARMY-01"
    
    def __init__(self, agent_id="accumulation_distribution_agent", config=None):
        """Initialize with optional config"""
        super().__init__(agent_id)
        
        # Use provided config or default
        self.config = config or AccumulationDistributionConfig()
        
        # Initialize Data Ingestion Agent for proper data access
        try:
            from agents.data_ingestion_agent import LiveDataGatewayAgent
            self.data_agent = LiveDataGatewayAgent()
            self.has_data_agent = True
            self.logger.info("Accumulation Distribution Agent: Using Data Ingestion Agent")
        except ImportError:
            self.data_agent = None
            self.has_data_agent = False
            self.logger.error("Failed to import Data Ingestion Agent")
    
    def execute_task(self, task):
        """Execute accumulation/distribution analysis task"""
        ticker = task.inputs.get("ticker", "UNKNOWN")
        return self.execute(ticker)
    
    def validate_inputs(self, task):
        """Validate task inputs"""
        return "ticker" in task.inputs
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        if not isinstance(outputs, dict):
            return {"valid": False, "error": "Output must be a dictionary"}
        
        quality_score = outputs.get("quality_score", 0.0)
        return {
            "valid": True,
            "quality_score": quality_score,
            "meets_threshold": quality_score >= 0.3
        }
    
    def execute(self, ticker: str) -> dict:
        """
        Execute accumulation/distribution analysis
        
        Args:
            ticker: Stock symbol to analyze
            
        Returns:
            Dictionary with analysis results
        """
        try:
            self.logger.info(f"Starting accumulation/distribution analysis for {ticker}")
            
            # Get data through Data Ingestion Agent (proper pattern)
            if not self.has_data_agent:
                return {
                    "status": "ERROR",
                    "error": "Data Ingestion Agent not available",
                    "ticker": ticker
                }
            
            # Fetch market data through the data agent
            data_result = self.data_agent.execute([ticker], source="schwab_mcp")
            
            if data_result.get('status') != 'OK':
                return {
                    "status": "ERROR", 
                    "error": f"Data fetch failed: {data_result.get('status')}",
                    "ticker": ticker
                }
            
            # Load price data from the generated files
            today_str = date.today().isoformat()
            bars_path = Path(f"data/live/{today_str}/{ticker}_bars.parquet")
            
            if not bars_path.exists():
                return {
                    "status": "ERROR",
                    "error": f"Price data file not found: {bars_path}",
                    "ticker": ticker
                }
            
            # Load and analyze price/volume data
            bars_df = pd.read_parquet(bars_path)
            self.logger.info(f"Loaded {len(bars_df)} price bars for {ticker}")
            
            # Perform accumulation/distribution analysis
            analysis = self._analyze_accumulation_distribution(bars_df, ticker)
            
            # Save results
            output_file = self._save_analysis_results(analysis, ticker)
            
            return {
                "status": "SUCCESS",
                "ticker": ticker,
                "output_file": str(output_file),
                "pattern": analysis.get("pattern", "unknown"),
                "confidence": analysis.get("confidence", 0.0),
                "strength": analysis.get("strength", "neutral"),
                "quality_score": analysis.get("quality_score", 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"Accumulation/distribution analysis failed for {ticker}: {e}")
            return {
                "status": "ERROR",
                "error": str(e),
                "ticker": ticker
            }
    
    def _analyze_accumulation_distribution(self, bars_df: pd.DataFrame, ticker: str) -> dict:
        """Analyze accumulation/distribution patterns from price/volume data"""
        try:
            # Ensure we have required columns
            if 'c' not in bars_df.columns or 'v' not in bars_df.columns:
                return self._basic_analysis_fallback(bars_df, ticker)
            
            # Calculate basic accumulation/distribution indicators
            closes = bars_df['c'].astype(float)
            volumes = bars_df['v'].astype(float)
            
            if len(closes) < 10:
                return self._basic_analysis_fallback(bars_df, ticker)
            
            # On-Balance Volume (OBV)
            price_changes = closes.diff()
            obv = []
            obv_value = 0
            
            for i, change in enumerate(price_changes):
                if pd.isna(change):
                    obv.append(obv_value)
                elif change > 0:
                    obv_value += volumes.iloc[i]
                elif change < 0:
                    obv_value -= volumes.iloc[i]
                obv.append(obv_value)
            
            obv_series = pd.Series(obv)
            
            # Accumulation/Distribution Line
            ad_line = []
            for i in range(len(bars_df)):
                if 'h' in bars_df.columns and 'l' in bars_df.columns:
                    high = float(bars_df['h'].iloc[i])
                    low = float(bars_df['l'].iloc[i])
                    close = float(bars_df['c'].iloc[i])
                    volume = float(bars_df['v'].iloc[i])
                    
                    if high != low:
                        clv = ((close - low) - (high - close)) / (high - low)
                        ad_value = clv * volume
                    else:
                        ad_value = 0
                else:
                    ad_value = 0
                    
                ad_line.append(ad_value)
            
            ad_series = pd.Series(ad_line)
            ad_cumulative = ad_series.cumsum()
            
            # Analyze trends
            recent_obv = obv_series.tail(5).mean()
            older_obv = obv_series.head(5).mean() if len(obv_series) > 10 else recent_obv
            
            recent_ad = ad_cumulative.tail(5).mean()
            older_ad = ad_cumulative.head(5).mean() if len(ad_cumulative) > 10 else recent_ad
            
            recent_price = closes.tail(5).mean()
            older_price = closes.head(5).mean() if len(closes) > 10 else recent_price
            
            # Determine pattern
            obv_trend = "rising" if recent_obv > older_obv * 1.05 else "falling" if recent_obv < older_obv * 0.95 else "sideways"
            ad_trend = "rising" if recent_ad > older_ad * 1.05 else "falling" if recent_ad < older_ad * 0.95 else "sideways"
            price_trend = "rising" if recent_price > older_price * 1.02 else "falling" if recent_price < older_price * 0.98 else "sideways"
            
            # Pattern classification
            if obv_trend == "rising" and ad_trend == "rising" and price_trend == "rising":
                pattern = "STRONG_ACCUMULATION"
                confidence = 0.8
            elif obv_trend == "rising" and ad_trend == "rising" and price_trend != "falling":
                pattern = "ACCUMULATION"
                confidence = 0.7
            elif obv_trend == "falling" and ad_trend == "falling" and price_trend == "falling":
                pattern = "STRONG_DISTRIBUTION"
                confidence = 0.8
            elif obv_trend == "falling" and ad_trend == "falling" and price_trend != "rising":
                pattern = "DISTRIBUTION"
                confidence = 0.7
            elif (obv_trend == "rising" and price_trend == "falling") or (ad_trend == "rising" and price_trend == "falling"):
                pattern = "HIDDEN_ACCUMULATION"
                confidence = 0.6
            elif (obv_trend == "falling" and price_trend == "rising") or (ad_trend == "falling" and price_trend == "rising"):
                pattern = "HIDDEN_DISTRIBUTION"
                confidence = 0.6
            else:
                pattern = "NEUTRAL"
                confidence = 0.4
            
            # Strength assessment
            if confidence > 0.75:
                strength = "STRONG"
            elif confidence > 0.6:
                strength = "MODERATE"
            else:
                strength = "WEAK"
            
            # Quality score based on data completeness
            quality_score = min(1.0, len(bars_df) / 50)
            
            return {
                "pattern": pattern,
                "confidence": confidence,
                "strength": strength,
                "obv_trend": obv_trend,
                "ad_trend": ad_trend,
                "price_trend": price_trend,
                "quality_score": quality_score,
                "bars_analyzed": len(bars_df),
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Analysis failed: {e}")
            return self._basic_analysis_fallback(bars_df, ticker)
    
    def _basic_analysis_fallback(self, bars_df: pd.DataFrame, ticker: str) -> dict:
        """Fallback analysis when detailed analysis fails"""
        return {
            "pattern": "UNKNOWN",
            "confidence": 0.1,
            "strength": "WEAK",
            "obv_trend": "unknown",
            "ad_trend": "unknown",
            "price_trend": "unknown",
            "quality_score": 0.1,
            "bars_analyzed": len(bars_df),
            "analysis_timestamp": datetime.now().isoformat(),
            "note": "Limited analysis due to data issues"
        }
    
    def _save_analysis_results(self, analysis: dict, ticker: str) -> Path:
        """Save analysis results to output file"""
        try:
            # Create output directory
            today_str = date.today().isoformat()
            output_dir = Path(f"army_analysis/{today_str}")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Save analysis
            output_file = output_dir / f"{ticker}_accumulation_distribution.json"
            
            with open(output_file, 'w') as f:
                import json
                json.dump(analysis, f, indent=2)
            
            self.logger.info(f"Analysis saved to: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Failed to save analysis: {e}")
            return Path(f"army_analysis/{ticker}_accumulation_distribution.json")


if __name__ == "__main__":
    """Test Accumulation Distribution Agent directly"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Accumulation Distribution Agent")
    parser.add_argument("--ticker", default="SPY", help="Ticker to analyze")
    parser.add_argument("--verbose", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and execute agent
    agent = AccumulationDistributionAgent()
    
    try:
        result = agent.execute(args.ticker.upper())
        
        print("SUCCESS: Accumulation/distribution analysis completed")
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"ERROR: Analysis failed: {e}")
        import traceback
        traceback.print_exc()
