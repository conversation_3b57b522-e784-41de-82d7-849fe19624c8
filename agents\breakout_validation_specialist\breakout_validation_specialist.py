#!/usr/bin/env python3
"""
Breakout Validation Agent - FIXED
Gets data through Data Ingestion Agent like all other agents
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any
from dataclasses import dataclass
from datetime import datetime, date
import sys
from pathlib import Path

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from agents.agent_base import BaseAgent

@dataclass
class BreakoutValidationConfig:
    """Configuration for breakout validation"""
    timeout_ms: int = 5000
    precision_threshold: float = 0.001
    enable_validation: bool = True


class BreakoutValidationAgent(BaseAgent):
    """
    Breakout Validation Agent
    Gets data through Data Ingestion Agent like all other agents
    """
    
    task_id = "ARMY-02"
    
    def __init__(self, agent_id="breakout_validation_agent", config=None):
        """Initialize with optional config"""
        super().__init__(agent_id)
        
        # Use provided config or default
        self.config = config or BreakoutValidationConfig()
        
        # Initialize Data Ingestion Agent for proper data access
        try:
            from agents.data_ingestion_agent import LiveDataGatewayAgent
            self.data_agent = LiveDataGatewayAgent()
            self.has_data_agent = True
            self.logger.info("Breakout Validation Agent: Using Data Ingestion Agent")
        except ImportError:
            self.data_agent = None
            self.has_data_agent = False
            self.logger.error("Failed to import Data Ingestion Agent")
    
    def execute_task(self, task):
        """Execute breakout validation task"""
        ticker = task.inputs.get("ticker", "UNKNOWN")
        return self.execute(ticker)
    
    def validate_inputs(self, task):
        """Validate task inputs"""
        return "ticker" in task.inputs
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        if not isinstance(outputs, dict):
            return {"valid": False, "error": "Output must be a dictionary"}
        
        quality_score = outputs.get("quality_score", 0.0)
        return {
            "valid": True,
            "quality_score": quality_score,
            "meets_threshold": quality_score >= 0.3
        }
    
    def execute(self, ticker: str) -> dict:
        """
        Execute breakout validation analysis
        
        Args:
            ticker: Stock symbol to analyze
            
        Returns:
            Dictionary with validation results
        """
        try:
            self.logger.info(f"Starting breakout validation for {ticker}")
            
            # Get data through Data Ingestion Agent (proper pattern)
            if not self.has_data_agent:
                return {
                    "status": "ERROR",
                    "error": "Data Ingestion Agent not available",
                    "ticker": ticker
                }
            
            # Fetch market data through the data agent
            data_result = self.data_agent.execute([ticker], source="schwab_mcp")
            
            if data_result.get('status') != 'OK':
                return {
                    "status": "ERROR", 
                    "error": f"Data fetch failed: {data_result.get('status')}",
                    "ticker": ticker
                }
            
            # Load price data from the generated files
            today_str = date.today().isoformat()
            bars_path = Path(f"data/live/{today_str}/{ticker}_bars.parquet")
            
            if not bars_path.exists():
                return {
                    "status": "ERROR",
                    "error": f"Price data file not found: {bars_path}",
                    "ticker": ticker
                }
            
            # Load and analyze price/volume data
            bars_df = pd.read_parquet(bars_path)
            self.logger.info(f"Loaded {len(bars_df)} price bars for {ticker}")
            
            # Perform breakout validation
            validation = self._validate_breakout(bars_df, ticker)
            
            # Save results
            output_file = self._save_validation_results(validation, ticker)
            
            return {
                "status": "SUCCESS",
                "ticker": ticker,
                "output_file": str(output_file),
                "breakout_validity": validation.get("breakout_validity", 0.0),
                "confidence": validation.get("confidence", 0.0),
                "volume_confirmation": validation.get("volume_confirmation", False),
                "quality_score": validation.get("quality_score", 0.0)
            }
            
        except Exception as e:
            self.logger.error(f"Breakout validation failed for {ticker}: {e}")
            return {
                "status": "ERROR",
                "error": str(e),
                "ticker": ticker
            }
    
    def _validate_breakout(self, bars_df: pd.DataFrame, ticker: str) -> dict:
        """Validate breakout authenticity from price/volume data"""
        try:
            # Ensure we have required columns
            if 'c' not in bars_df.columns or 'v' not in bars_df.columns:
                return self._basic_validation_fallback(bars_df, ticker)
            
            closes = bars_df['c'].astype(float)
            volumes = bars_df['v'].astype(float)
            
            if len(closes) < 20:
                return self._basic_validation_fallback(bars_df, ticker)
            
            # Get highs and lows if available
            if 'h' in bars_df.columns and 'l' in bars_df.columns:
                highs = bars_df['h'].astype(float)
                lows = bars_df['l'].astype(float)
            else:
                highs = closes
                lows = closes
            
            # Calculate recent price action
            recent_period = min(10, len(closes) // 2)
            recent_closes = closes.tail(recent_period)
            recent_volumes = volumes.tail(recent_period)
            recent_highs = highs.tail(recent_period)
            
            # Historical baseline
            historical_closes = closes.head(-recent_period) if len(closes) > recent_period else closes
            historical_volumes = volumes.head(-recent_period) if len(volumes) > recent_period else volumes
            historical_highs = highs.head(-recent_period) if len(highs) > recent_period else highs
            
            # Breakout analysis
            current_price = closes.iloc[-1]
            recent_high = recent_highs.max()
            historical_resistance = historical_highs.quantile(0.95)
            
            # Volume analysis
            avg_historical_volume = historical_volumes.mean()
            recent_avg_volume = recent_volumes.mean()
            volume_ratio = recent_avg_volume / avg_historical_volume if avg_historical_volume > 0 else 1.0
            
            # Price momentum
            price_change = (current_price - closes.iloc[0]) / closes.iloc[0] if closes.iloc[0] > 0 else 0
            recent_momentum = (recent_closes.iloc[-1] - recent_closes.iloc[0]) / recent_closes.iloc[0] if recent_closes.iloc[0] > 0 else 0
            
            # Breakout validation criteria
            breakout_score = 0.0
            confidence_factors = []
            
            # 1. Price above resistance
            if current_price > historical_resistance:
                breakout_score += 30
                confidence_factors.append("price_above_resistance")
            
            # 2. Volume confirmation
            volume_confirmation = volume_ratio > 1.2
            if volume_confirmation:
                breakout_score += 25
                confidence_factors.append("volume_confirmation")
            
            # 3. Momentum strength
            if recent_momentum > 0.02:  # 2% momentum
                breakout_score += 20
                confidence_factors.append("strong_momentum")
            elif recent_momentum > 0:
                breakout_score += 10
                confidence_factors.append("positive_momentum")
            
            # 4. Sustained move
            recent_above_resistance = sum(recent_closes > historical_resistance) / len(recent_closes)
            if recent_above_resistance > 0.7:
                breakout_score += 15
                confidence_factors.append("sustained_move")
            
            # 5. Overall trend
            if price_change > 0.05:  # 5% overall gain
                breakout_score += 10
                confidence_factors.append("strong_trend")
            
            # Normalize to 0-100 scale
            breakout_validity = min(100, max(0, breakout_score))
            
            # Confidence based on number of factors
            confidence = min(95, len(confidence_factors) * 20)
            
            # Quality score based on data completeness
            quality_score = min(1.0, len(bars_df) / 50)
            
            return {
                "breakout_validity": breakout_validity,
                "confidence": confidence,
                "volume_confirmation": volume_confirmation,
                "volume_ratio": volume_ratio,
                "price_momentum": recent_momentum,
                "current_price": current_price,
                "resistance_level": historical_resistance,
                "confidence_factors": confidence_factors,
                "quality_score": quality_score,
                "bars_analyzed": len(bars_df),
                "analysis_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Breakout validation failed: {e}")
            return self._basic_validation_fallback(bars_df, ticker)
    
    def _basic_validation_fallback(self, bars_df: pd.DataFrame, ticker: str) -> dict:
        """Fallback validation when detailed analysis fails"""
        return {
            "breakout_validity": 50.0,
            "confidence": 10.0,
            "volume_confirmation": False,
            "volume_ratio": 1.0,
            "price_momentum": 0.0,
            "current_price": 0.0,
            "resistance_level": 0.0,
            "confidence_factors": [],
            "quality_score": 0.1,
            "bars_analyzed": len(bars_df),
            "analysis_timestamp": datetime.now().isoformat(),
            "note": "Limited validation due to data issues"
        }
    
    def _save_validation_results(self, validation: dict, ticker: str) -> Path:
        """Save validation results to output file"""
        try:
            # Create output directory
            today_str = date.today().isoformat()
            output_dir = Path(f"army_analysis/{today_str}")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Save validation
            output_file = output_dir / f"{ticker}_breakout_validation.json"
            
            with open(output_file, 'w') as f:
                import json
                json.dump(validation, f, indent=2)
            
            self.logger.info(f"Validation saved to: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"Failed to save validation: {e}")
            return Path(f"army_analysis/{ticker}_breakout_validation.json")


if __name__ == "__main__":
    """Test Breakout Validation Agent directly"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Breakout Validation Agent")
    parser.add_argument("--ticker", default="SPY", help="Ticker to analyze")
    parser.add_argument("--verbose", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and execute agent
    agent = BreakoutValidationAgent()
    
    try:
        result = agent.execute(args.ticker.upper())
        
        print("SUCCESS: Breakout validation completed")
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"ERROR: Validation failed: {e}")
        import traceback
        traceback.print_exc()
