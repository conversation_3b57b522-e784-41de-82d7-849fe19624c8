import os
import requests
import pandas as pd
import json
import logging
import time
from pathlib import Path
from datetime import date
from agents.agent_base import BaseAgent

class LiveDataGatewayAgent(BaseAgent):
    task_id = "D-01"
    
    # Configuration constants - SCHWAB MCP ONLY
    SCHWAB_MCP_BASE = os.getenv("SCHWAB_MCP_URL", "http://localhost:8005")
    MCP_BASE = os.getenv("MCP_HTTP_URL", os.getenv("MCP_HOST", "http://localhost:8005"))
    
    # Source priority: Schwab MCP ONLY - No external fallbacks
    SOURCE_PRIORITY = ["schwab_mcp"]
    
    def __init__(self, agent_id="live_data_gateway_agent"):
        """Initialize Live Data Gateway Agent with Enhanced Agent Integration"""
        super().__init__(agent_id)
        self.logger = logging.getLogger(agent_id)
        
        # Try enhanced agent first, fallback to direct MCP
        try:
            from enhanced_data_agent_broker_integration import EnhancedDataAgent
            self.enhanced_agent = EnhancedDataAgent()
            self.real_time_agent = self.enhanced_agent
            self.has_real_time = True
            self.broker_integration_enabled = True
            self.logger.info("Enhanced Data Agent initialized - Broker API primary mode")
        except ImportError as e:
            self.enhanced_agent = None
            self.real_time_agent = None
            self.has_real_time = False
            self.broker_integration_enabled = False
            self.logger.warning(f"Enhanced agent not available: {e} - using direct MCP mode")
    
    def execute_task(self, task):
        """Execute the data ingestion task"""
        ticker_list = task.inputs.get("ticker_list", [])
        source = task.inputs.get("source", "schwab")
        api_key = task.inputs.get("api_key")
        bar_tf = task.inputs.get("bar_tf", "1")
        
        return self.execute(ticker_list, source=source, bar_tf=bar_tf, api_key=api_key)
    
    def validate_inputs(self, task):
        """Validate task inputs meet requirements"""
        inputs = task.inputs
        required = ["ticker_list"]
        has_required = all(key in inputs for key in required)
        
        # If using polygon, api_key is required
        if inputs.get("source") == "schwab" and not inputs.get("api_key"):
            return False
            
        return has_required
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        quality_metrics = {}
        
        if isinstance(outputs, dict) and outputs.get("status") == "OK":
            meta = outputs.get("meta", {})
            total_bars = sum(ticker_data.get("bars", 0) for ticker_data in meta.values())
            total_opts = sum(ticker_data.get("opts", 0) for ticker_data in meta.values())
            
            quality_metrics["data_retrieved"] = 1.0
            quality_metrics["bars_sufficient"] = 1.0 if total_bars >= 10 else 0.0
            quality_metrics["options_sufficient"] = 1.0 if total_opts >= 50 else 0.0
        else:
            quality_metrics["data_retrieved"] = 0.0
            quality_metrics["bars_sufficient"] = 0.0
            quality_metrics["options_sufficient"] = 0.0
        
        return quality_metrics
    
    def log_training(self, data):
        """Log training data for Agent Zero learning"""
        print(f"Training logged: {json.dumps(data, indent=2, default=str)}")
    
    def execute(self, ticker_list, *, source="auto", bar_tf="1", api_key=None):
        """
        Execute data ingestion with broker API priority and formulated fallback
        
        Source priority:
        1. "auto" - Use broker API primary with fallback chain
        2. "schwab" - Force Schwab broker API only
        3. "schwab" - Use MCP servers only
        4. "schwab" - Use Polygon API only
        """
        today = date.today().isoformat()
        out_dir = Path(f"data/live/{today}")
        out_dir.mkdir(parents=True, exist_ok=True)
        
        # If enhanced agent available and auto mode, use broker API integration
        if self.broker_integration_enabled and source == "auto":
            return self._execute_with_broker_integration(ticker_list, out_dir, bar_tf)
        
        # Legacy execution path for specific sources
        # REMOVED: Polygon source support - Schwab MCP only
        if source == "schwab":
            fetch_fn = lambda tk: self._pull_schwab_mcp(tk, bar_tf)
        else:
            fetch_fn = lambda tk: self._pull_mcp(tk, bar_tf)
        
        meta = {}
        successful_tickers = []
        
        for tk in ticker_list:
            try:
                bars, opts = fetch_fn(tk)
                
                # Save to parquet files
                bars_path = out_dir / f"{tk}_bars.parquet"
                opts_path = out_dir / f"{tk}_options.parquet"
                
                bars.to_parquet(bars_path, index=False)
                opts.to_parquet(opts_path, index=False)
                
                meta[tk] = {
                    "bars": len(bars), 
                    "opts": len(opts),
                    "source": source,
                    "bars_file": str(bars_path),
                    "opts_file": str(opts_path)
                }
                successful_tickers.append(tk)
                
            except Exception as e:
                print(f"Failed to fetch data for {tk}: {e}")
                meta[tk] = {"error": str(e), "source": source}
        
        result = {
            "status": "OK" if successful_tickers else "PARTIAL",
            "meta": meta,
            "source_used": source,
            "successful_tickers": successful_tickers,
            "output_dir": str(out_dir)
        }
        
        # Log training data
        self.log_training({
            "inputs": {"ticker_list": ticker_list, "source": source},
            "outputs": result
        })
        
        return result
    
    def _execute_with_broker_integration(self, ticker_list, out_dir, bar_tf):
        """Execute using enhanced broker API integration with formulated fallback"""
        meta = {}
        successful_tickers = []
        source_usage = {"schwab": 0, "schwab": 0, "formulated": 0, "failed": 0}
        
        for tk in ticker_list:
            try:
                # Get market data with automatic source selection and fallback
                market_data_result = self.enhanced_agent.get_market_data(tk, use_fallback=True)
                
                if market_data_result["data"]:
                    # Extract data based on source
                    data_source = market_data_result["source"]
                    market_data = market_data_result["data"]
                    
                    # Track source usage
                    if "schwab" in data_source:
                        source_usage["schwab"] += 1
                    elif "schwab" in data_source:
                        source_usage["schwab"] += 1
                    elif "formulated" in data_source:
                        source_usage["formulated"] += 1
                    
                    # Get bars and options using best available source
                    bars, opts = self._get_comprehensive_data(tk, data_source, bar_tf)
                    
                    # Enhance data with broker bid/ask if available
                    if 'bid' in market_data and 'ask' in market_data:
                        # Add real bid/ask to bars data
                        if not bars.empty:
                            bars['real_bid'] = market_data['bid']
                            bars['real_ask'] = market_data['ask']
                            bars['spread_pct'] = ((market_data['ask'] - market_data['bid']) / market_data['last_price']) * 100
                            
                            # Handle quality metrics properly
                            quality_metrics = market_data_result.get('quality_metrics')
                            if quality_metrics:
                                if hasattr(quality_metrics, 'overall_quality'):
                                    bars['data_quality'] = quality_metrics.overall_quality
                                elif isinstance(quality_metrics, dict):
                                    bars['data_quality'] = quality_metrics.get('overall_quality', 0.0)
                                else:
                                    bars['data_quality'] = 0.0
                            else:
                                bars['data_quality'] = 0.0
                    
                    # Save enhanced data
                    bars_path = out_dir / f"{tk}_bars.parquet"
                    opts_path = out_dir / f"{tk}_options.parquet"
                    
                    bars.to_parquet(bars_path, index=False)
                    opts.to_parquet(opts_path, index=False)
                    
                    meta[tk] = {
                        "bars": len(bars),
                        "opts": len(opts),
                        "data_source": data_source,
                        "has_real_bid_ask": 'bid' in market_data and 'ask' in market_data,
                        "bars_file": str(bars_path),
                        "opts_file": str(opts_path)
                    }
                    
                    # Handle quality score properly
                    quality_metrics = market_data_result.get('quality_metrics')
                    if quality_metrics:
                        if hasattr(quality_metrics, 'overall_quality'):
                            meta[tk]["quality_score"] = quality_metrics.overall_quality
                        elif isinstance(quality_metrics, dict):
                            meta[tk]["quality_score"] = quality_metrics.get('overall_quality', 0.0)
                        else:
                            meta[tk]["quality_score"] = 0.0
                    else:
                        meta[tk]["quality_score"] = 0.0
                    successful_tickers.append(tk)
                    
                else:
                    source_usage["failed"] += 1
                    self.logger.error(f"Failed to get market data for {tk}")
                    meta[tk] = {"error": "No data from any source", "data_source": "none"}
                    
            except Exception as e:
                source_usage["failed"] += 1
                self.logger.error(f"Error processing {tk}: {str(e)}")
                meta[tk] = {"error": str(e), "data_source": "error"}
        
        result = {
            "status": "OK" if successful_tickers else "PARTIAL",
            "meta": meta,
            "successful_tickers": successful_tickers,
            "source_used": "broker_api_integration",
            "source_usage": source_usage,
            "enhancement": "real_bid_ask_data",
            "output_dir": str(out_dir)
        }
        
        # Log training data
        self.log_training({
            "inputs": {"ticker_list": ticker_list, "mode": "broker_integration"},
            "outputs": result
        })
        
        return result
    
    def _get_comprehensive_data(self, ticker, data_source, bar_tf):
        """Get comprehensive bars and options data from best available source"""
        try:
            # Try Schwab MCP first for comprehensive data
            if "schwab" in data_source or data_source == "auto":
                try:
                    return self._pull_schwab_mcp(ticker, bar_tf)
                except Exception as e:
                    self.logger.warning(f"Schwab MCP failed for {ticker}: {str(e)}")
            
            # Fallback to regular MCP
            try:
                return self._pull_mcp(ticker, bar_tf)
            except Exception as e:
                self.logger.warning(f"Regular MCP failed for {ticker}: {str(e)}")
            
            # Last resort: empty dataframes
            return pd.DataFrame(), pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"Error getting comprehensive data for {ticker}: {str(e)}")
            return pd.DataFrame(), pd.DataFrame()
    
    def _pull_schwab_mcp(self, ticker, bar_tf):
        """Pull data from Schwab MCP server using HTTP REST endpoints"""
        try:
            # HTTP REST request for quotes (bars data)
            quotes_response = requests.get(
                f"{self.SCHWAB_MCP_BASE}/quotes/{ticker}",
                timeout=15
            )
            
            if quotes_response.status_code == 200:
                quote_data = quotes_response.json()
                
                # Convert quote to bars format
                bars_df = pd.DataFrame([{
                    't': int(quote_data.get('timestamp', time.time())),
                    'o': quote_data.get('last_price', 0),
                    'h': quote_data.get('last_price', 0), 
                    'l': quote_data.get('last_price', 0),
                    'c': quote_data.get('last_price', 0),
                    'v': quote_data.get('volume', 0),
                    'ticker': ticker.upper()
                }])
            else:
                bars_df = pd.DataFrame()
            
            # HTTP REST request for options
            options_response = requests.get(
                f"{self.SCHWAB_MCP_BASE}/options/{ticker}",
                timeout=15
            )
            
            if options_response.status_code == 200:
                options_data = options_response.json()
                
                # Convert options chain to DataFrame
                options_list = []
                
                # Process calls
                for call in options_data.get('calls', []):
                    options_list.append({
                        'ticker': ticker.upper(),
                        'strike': call.get('strike'),
                        'bid': call.get('bid'),
                        'ask': call.get('ask'),
                        'last': call.get('last'),
                        'volume': call.get('volume', 0),
                        'open_interest': call.get('open_interest', 0),
                        'type': 'call'
                    })
                
                # Process puts
                for put in options_data.get('puts', []):
                    options_list.append({
                        'ticker': ticker.upper(),
                        'strike': put.get('strike'),
                        'bid': put.get('bid'),
                        'ask': put.get('ask'),
                        'last': put.get('last'),
                        'volume': put.get('volume', 0),
                        'open_interest': put.get('open_interest', 0),
                        'type': 'put'
                    })
                
                opts_df = pd.DataFrame(options_list)
            else:
                opts_df = pd.DataFrame()
            
            return bars_df, opts_df
            
            return bars_df, opts_df
            
        except Exception as e:
            self.logger.error(f"Schwab MCP error for {ticker}: {str(e)}")
            raise
            return pd.DataFrame(), pd.DataFrame()
    
    # ---------- MCP implementation ----------
    def _pull_mcp(self, ticker, tf):
        """Pull data via MCP server - Direct access to Schwab MCP"""
        try:
            # Use direct MCP access since we have working MCP server
            import requests
            import json
            
            # Get price history
            price_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": "get_price_history",
                    "arguments": {
                        "symbol": ticker,
                        "period_type": "day",
                        "period": 2,
                        "frequency_type": "minute", 
                        "frequency": 5
                    }
                }
            }
            
            response = requests.post(
                self.SCHWAB_MCP_BASE,
                json=price_request,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            bars = pd.DataFrame()
            if response.status_code == 200:
                data = response.json()
                if 'result' in data:
                    content = data['result']['content'][0]['text']
                    result = json.loads(content)
                    if 'bars' in result:
                        bars = pd.DataFrame(result['bars'])
            
            # Get options data
            options_request = {
                "jsonrpc": "2.0", 
                "id": 2,
                "method": "tools/call",
                "params": {
                    "name": "get_option_chain",
                    "arguments": {
                        "symbol": ticker,
                        "contract_type": "ALL",
                        "strike_count": 10
                    }
                }
            }
            
            response = requests.post(
                self.SCHWAB_MCP_BASE,
                json=options_request,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            opts = pd.DataFrame()
            if response.status_code == 200:
                data = response.json()
                if 'result' in data:
                    content = data['result']['content'][0]['text']
                    result = json.loads(content)
                    # Parse options data structure
                    option_data = result.get('option_data', {})
                    if option_data:
                        # Convert options data to DataFrame format
                        opts_list = []
                        for exp_date, strikes in option_data.items():
                            if isinstance(strikes, dict):
                                for strike, contract in strikes.items():
                                    if isinstance(contract, dict):
                                        opts_list.append(contract)
                        if opts_list:
                            opts = pd.DataFrame(opts_list)
            
            # Validate we got data
            if bars.empty:
                raise ValueError("No price data returned from MCP")
                
            return bars, opts
            
        except Exception as e:
            # NO EXTERNAL API FALLBACK - Force proper Schwab MCP configuration
            self.logger.error(f"Schwab MCP connection failed: {e}")
            self.logger.error("SYSTEM DESIGN: All data must come from Schwab MCP only")
            raise ValueError(f"Schwab MCP unavailable - no external fallbacks allowed: {e}")

    # ---------- REMOVED: Polygon fallback eliminated ----------


def main():
    """Command-line interface for LiveDataGatewayAgent"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Live Data Gateway Agent")
    parser.add_argument("--tickers", nargs="+", required=True, help="List of ticker symbols")
    parser.add_argument("--source", choices=["schwab", "schwab"], default="schwab", help="Data source")
    parser.add_argument("--timeframe", default="1", help="Bar timeframe in minutes")
    parser.add_argument("--api-key", help="API key (for Polygon)")
    
    args = parser.parse_args()
    
    # Create agent and execute
    agent = LiveDataGatewayAgent()
    
    try:
        result = agent.execute(
            ticker_list=args.tickers,
            source=args.source,
            bar_tf=args.timeframe,
            api_key=args.api_key
        )
        
        print("SUCCESS: DATA INGESTION COMPLETED")
        print("=" * 50)
        print(f"Source: {result['source_used']}")
        print(f"Status: {result['status']}")
        print(f"Output Directory: {result['output_dir']}")
        print(f"Successful Tickers: {result['successful_tickers']}")
        
        print("\nData Summary:")
        for ticker, data in result['meta'].items():
            if 'error' not in data:
                print(f"  {ticker}: {data['bars']} bars, {data['opts']} options")
            else:
                print(f"  {ticker}: ERROR - {data['error']}")
        
        return 0
        
    except Exception as e:
        print(f"ERROR: Data ingestion failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
