#!/usr/bin/env python3
"""
Mean Reversion Specialist Agent
Mathematical Trading Analysis with Statistical Rigor

Implementation Date: 2025-06-24
Mathematical Foundation: SuperSmoother + MDX + Statistical Analysis  
Precision Standard: IEEE 754 + Statistical Rigor (1e-10 tolerance)
Performance Budget: 5 seconds execution
"""

import math
import time
import uuid
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from datetime import datetime

from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus

logger = logging.getLogger(__name__)

@dataclass
class MeanReversionConfig:
    """Configuration for Mean Reversion Specialist Agent"""
    # Performance parameters
    timeout_ms: int = 5000
    precision_tolerance: float = 1e-10
    
    # Mathematical parameters
    supersmoother_length: int = 21
    atr_length: int = 26
    stddev_length: int = 26
    composite_length: int = 21
    
    # Pi-based channel multipliers (IMMUTABLE CONSTANTS)
    pi_inner_multiplier: float = 1.0      #  * 1.0 = 3.14159...
    pi_outer_multiplier: float = 2.415    #  * 2.415 = 7.58858...
    
    # Statistical thresholds
    z_score_significant: float = 1.5      # Statistical significance
    z_score_extreme: float = 2.0          # Extreme deviation
    
    # Composite mean weights
    supersmoother_weight: float = 0.5
    ema_weight: float = 0.3
    sma_weight: float = 0.2

class MeanReversionSpecialist(BaseAgent):
    """
    Mean Reversion Specialist Agent
    
    Implements mathematical precision mean reversion analysis using:
    - SuperSmoother algorithm (Ehlers)
    - ATR-filtered EMA (MDX methodology)
    - Z-score statistical analysis
    - Pi-based channel calculations
    
    Mathematical Requirements:
    - Precision: 1e-10 minimum tolerance
    - Performance: 5 seconds execution
    - Statistical rigor: IEEE 754 compliance
    """
    
    def __init__(self, config: Optional[MeanReversionConfig] = None):
        super().__init__(agent_id="mean_reversion_specialist")
        self.config = config or MeanReversionConfig()
        self.logger = logger
        
        # Mathematical constants (IMMUTABLE)
        self.PI = math.pi  # 3.141592653589793
        
        # Performance tracking
        self.calculation_cache = {}
        self.precision_metrics = {}
        
        self.logger.info("Mean Reversion Specialist Agent initialized")
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """
        Validate task inputs meet mathematical requirements
        
        Args:
            task: AgentTask with market data inputs
            
        Returns:
            bool: True if inputs valid for mathematical analysis
        """
        try:
            inputs = task.inputs
            
            # Required fields validation
            required_fields = ['market_data']
            for field in required_fields:
                if field not in inputs:
                    self.logger.error(f"Missing required field: {field}")
                    return False
            
            market_data = inputs['market_data']
            
            # Market data structure validation
            required_market_fields = ['high', 'low', 'close', 'volume']
            for field in required_market_fields:
                if field not in market_data:
                    self.logger.error(f"Missing market data field: {field}")
                    return False
            
            # Data length validation (minimum for calculations)
            min_length = max(self.config.supersmoother_length, 
                           self.config.atr_length, 
                           self.config.composite_length) + 2
            
            for field in required_market_fields:
                if len(market_data[field]) < min_length:
                    self.logger.error(f"Insufficient data length for {field}: {len(market_data[field])} < {min_length}")
                    return False
            
            # Numerical validation
            for field in required_market_fields:
                data_array = np.array(market_data[field])
                if np.any(np.isnan(data_array)) or np.any(np.isinf(data_array)):
                    self.logger.error(f"Invalid numerical data in {field}")
                    return False
                
                if np.any(data_array <= 0):
                    self.logger.error(f"Non-positive values in {field}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Input validation failed: {e}")
            return False
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """
        Validate outputs meet mathematical precision standards
        
        Args:
            outputs: Task execution outputs
            
        Returns:
            Dict[str, float]: Quality metrics for mathematical validation
        """
        quality_metrics = {}
        
        try:
            # Check for required output fields
            required_fields = [
                'composite_mean', 'supersmoother_mean', 'atr_filtered_mean',
                'z_score', 'channel_upper_inner', 'channel_lower_inner',
                'channel_upper_outer', 'channel_lower_outer', 'distance_percentage',
                'mdx_value', 'statistical_significance', 'extreme_condition',
                'mean_reversion_signal'
            ]
            
            completeness = sum(1 for field in required_fields if field in outputs) / len(required_fields)
            quality_metrics['completeness'] = completeness
            
            # Mathematical precision validation
            precision_score = 1.0
            for field in required_fields:
                if field in outputs and isinstance(outputs[field], (int, float)):
                    value = outputs[field]
                    
                    # NaN/Infinity check
                    if math.isnan(value) or math.isinf(value):
                        precision_score = 0.0
                        break
                    
                    # Precision threshold check
                    if abs(value) > 0 and abs(value) < self.config.precision_tolerance:
                        precision_score = min(precision_score, 0.9)
            
            quality_metrics['mathematical_precision'] = precision_score
            
            # Statistical validity checks
            if 'z_score' in outputs:
                z_score = outputs['z_score']
                # Reasonable z-score bounds (-5 to 5)
                if abs(z_score) > 5.0:
                    quality_metrics['statistical_validity'] = 0.5
                else:
                    quality_metrics['statistical_validity'] = 1.0
            else:
                quality_metrics['statistical_validity'] = 0.0
            
            # Channel validation (inner < outer)
            if all(field in outputs for field in ['channel_upper_inner', 'channel_upper_outer']):
                if outputs['channel_upper_inner'] <= outputs['channel_upper_outer']:
                    quality_metrics['channel_logic'] = 1.0
                else:
                    quality_metrics['channel_logic'] = 0.0
            else:
                quality_metrics['channel_logic'] = 0.0
            
            # Overall quality score
            avg_quality = sum(quality_metrics.values()) / len(quality_metrics)
            quality_metrics['overall_quality'] = avg_quality
            
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"Output validation failed: {e}")
            return {'error': 0.0, 'overall_quality': 0.0}
    
    def execute(self, ticker: str) -> Dict[str, Any]:
        """
        Execute mean reversion analysis for ultimate orchestrator compatibility

        Args:
            ticker: Stock symbol to analyze

        Returns:
            Dict: Mean reversion analysis results
        """
        try:
            # Create a simple task for compatibility
            task_data = {
                'symbol': ticker,
                'task_type': 'mean_reversion_analysis',
                'timestamp': datetime.now().isoformat()
            }

            # Create AgentTask
            task = AgentTask(
                task_id=str(uuid.uuid4()),
                agent_id=self.agent_id,
                task_type='mean_reversion_analysis',
                inputs=task_data,
                priority=1,
                created_at=datetime.now()
            )

            # Execute the task
            result = self.execute_task(task)

            if result.status == TaskStatus.COMPLETED:
                return result.outputs
            else:
                return {'error': f'Mean reversion analysis failed: {result.outputs.get("error", "Unknown error")}'}

        except Exception as e:
            self.logger.error(f"Mean reversion execute failed: {e}")
            return {'error': str(e)}

    def execute_task(self, task: AgentTask) -> AgentResult:
        """
        Execute mean reversion analysis with mathematical precision
        
        Args:
            task: AgentTask containing market data
            
        Returns:
            AgentResult: Complete mean reversion analysis
        """
        start_time = time.time()
        
        try:
            market_data = task.inputs['market_data']
            
            # Convert to numpy arrays for mathematical operations
            high = np.array(market_data['high'], dtype=np.float64)
            low = np.array(market_data['low'], dtype=np.float64)
            close = np.array(market_data['close'], dtype=np.float64)
            volume = np.array(market_data['volume'], dtype=np.float64)
            
            # Calculate HLC3 (typical price)
            hlc3 = (high + low + close) / 3.0
            
            # Step 1: Calculate SuperSmoother mean
            supersmoother_mean = self._calculate_supersmoother(hlc3, self.config.supersmoother_length)
            
            # Step 2: Calculate ATR-filtered EMA (MDX style)
            atr_filtered_mean = self._calculate_atr_filtered_ema(hlc3, high, low, 
                                                               self.config.composite_length, 
                                                               self.config.atr_length)
            
            # Step 3: Calculate simple moving average
            sma_mean = self._calculate_sma(hlc3, self.config.composite_length)
            
            # Step 4: Calculate composite mean with weights
            composite_mean = (supersmoother_mean * self.config.supersmoother_weight + 
                            atr_filtered_mean * self.config.ema_weight + 
                            sma_mean * self.config.sma_weight)
            
            # Step 5: Calculate ATR for channel calculations
            atr_range = self._calculate_atr(high, low, close, self.config.atr_length)
            
            # Step 6: Calculate Pi-based channels
            channels = self._calculate_pi_channels(composite_mean, atr_range)
            
            # Step 7: Calculate Z-score for statistical analysis
            z_score = self._calculate_z_score(close[-1], composite_mean, hlc3, self.config.composite_length)
            
            # Step 8: Calculate distance percentage from mean
            distance_percentage = ((close[-1] - composite_mean) / composite_mean) * 100.0
            
            # Step 9: Calculate MDX value (Mean Deviation Index)
            mdx_value = self._calculate_mdx(hlc3, composite_mean, atr_range)
            
            # Step 10: Determine statistical significance and conditions
            statistical_significance = abs(z_score) > self.config.z_score_significant
            extreme_condition = abs(z_score) > self.config.z_score_extreme
            
            # Step 11: Generate mean reversion signal
            mean_reversion_signal = self._generate_signal(close[-1], composite_mean, z_score, channels)
            
            # Compile results
            outputs = {
                'composite_mean': float(composite_mean),
                'supersmoother_mean': float(supersmoother_mean),
                'atr_filtered_mean': float(atr_filtered_mean),
                'sma_mean': float(sma_mean),
                'z_score': float(z_score),
                'channel_upper_inner': channels['upper_inner'],
                'channel_lower_inner': channels['lower_inner'],
                'channel_upper_outer': channels['upper_outer'],
                'channel_lower_outer': channels['lower_outer'],
                'distance_percentage': float(distance_percentage),
                'mdx_value': float(mdx_value),
                'atr_range': float(atr_range),
                'statistical_significance': bool(statistical_significance),
                'extreme_condition': bool(extreme_condition),
                'mean_reversion_signal': mean_reversion_signal,
                'current_price': float(close[-1]),
                'mathematical_validation': True,
                'calculation_timestamp': datetime.now().isoformat()
            }
            
            execution_time = time.time() - start_time
            
            # Performance validation
            if execution_time > (self.config.timeout_ms / 1000.0):
                self.logger.warning(f"Execution time {execution_time:.3f}s exceeded budget {self.config.timeout_ms/1000.0}s")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                outputs=outputs,
                execution_time=execution_time,
                quality_metrics={}  # Will be populated by validate_outputs
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Mean reversion analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )
    
    def _calculate_supersmoother(self, src: np.ndarray, length: int) -> float:
        """
        Calculate SuperSmoother using Ehlers algorithm
        
        Mathematical Formula (EXACT IMPLEMENTATION REQUIRED):
        a1 = exp(-2 *  / length)
        b1 = 2 * a1 * cos(2 *  / length)
        c3 = -a1
        c2 = b1
        c1 = 1 - c2 - c3
        
        Args:
            src: Source price data array
            length: SuperSmoother period
            
        Returns:
            float: SuperSmoother value with 1e-10 precision
        """
        try:
            # Ehlers SuperSmoother coefficients (EXACT FORMULA)
            a1 = math.exp(-math.sqrt(2) * math.pi / length)
            b1 = 2 * a1 * math.cos(math.sqrt(2) * math.pi / length)
            c3 = -math.pow(a1, 2)
            c2 = b1
            c1 = 1 - c2 - c3
            
            # Initialize SuperSmoother calculation
            ss_values = np.zeros_like(src)
            
            # First few values use simple average
            for i in range(min(3, len(src))):
                ss_values[i] = src[i]
            
            # Calculate SuperSmoother using recursive formula
            for i in range(2, len(src)):
                ss_values[i] = (c1 * src[i] + 
                              c2 * ss_values[i-1] + 
                              c3 * ss_values[i-2])
            
            return float(ss_values[-1])
            
        except Exception as e:
            self.logger.error(f"SuperSmoother calculation failed: {e}")
            # Fallback to simple moving average
            return float(np.mean(src[-length:]))
    
    def _calculate_atr_filtered_ema(self, src: np.ndarray, high: np.ndarray, 
                                  low: np.ndarray, period: int, atr_len: int) -> float:
        """
        Calculate ATR-filtered EMA using MDX methodology
        
        Mathematical Formula:
        stddev_factor = min(lowest(stddev_atr, 26) / stddev_atr, 5.0)
        alpha = (2.0 * stddev_factor) / (period + 1.0)
        
        Args:
            src: Source price data
            high: High price data
            low: Low price data  
            period: EMA period
            atr_len: ATR calculation length
            
        Returns:
            float: ATR-filtered EMA value
        """
        try:
            # Calculate ATR
            atr_values = self._calculate_atr_series(high, low, 
                                                   np.concatenate([[high[0]], src[1:]]), 
                                                   atr_len)
            
            # Calculate standard deviation of ATR
            if len(atr_values) >= 26:
                stddev_atr = np.std(atr_values[-26:])
                lowest_stddev = np.min(np.std(atr_values[i:i+26]) 
                                     for i in range(max(0, len(atr_values)-52), 
                                                   len(atr_values)-25))
            else:
                stddev_atr = np.std(atr_values)
                lowest_stddev = stddev_atr
            
            # Calculate volatility factor (MDX methodology)
            if stddev_atr > 0:
                stddev_factor = min(lowest_stddev / stddev_atr, 5.0)
            else:
                stddev_factor = 1.0
            
            # Calculate adaptive alpha
            alpha = (2.0 * stddev_factor) / (period + 1.0)
            
            # Calculate ATR-filtered EMA
            ema_values = np.zeros_like(src)
            ema_values[0] = src[0]
            
            for i in range(1, len(src)):
                ema_values[i] = alpha * src[i] + (1.0 - alpha) * ema_values[i-1]
            
            return float(ema_values[-1])
            
        except Exception as e:
            self.logger.error(f"ATR-filtered EMA calculation failed: {e}")
            # Fallback to simple EMA
            return self._calculate_simple_ema(src, period)
    
    def _calculate_simple_ema(self, src: np.ndarray, period: int) -> float:
        """Calculate simple exponential moving average"""
        alpha = 2.0 / (period + 1.0)
        ema = src[0]
        
        for value in src[1:]:
            ema = alpha * value + (1.0 - alpha) * ema
            
        return float(ema)
    
    def _calculate_sma(self, src: np.ndarray, length: int) -> float:
        """
        Calculate Simple Moving Average
        
        Args:
            src: Source data array
            length: SMA period
            
        Returns:
            float: Simple moving average
        """
        return float(np.mean(src[-length:]))
    
    def _calculate_atr(self, high: np.ndarray, low: np.ndarray, 
                      close: np.ndarray, length: int) -> float:
        """
        Calculate Average True Range
        
        Args:
            high: High price array
            low: Low price array
            close: Close price array
            length: ATR period
            
        Returns:
            float: ATR value
        """
        try:
            atr_values = self._calculate_atr_series(high, low, close, length)
            return float(np.mean(atr_values[-length:]))
            
        except Exception as e:
            self.logger.error(f"ATR calculation failed: {e}")
            return float(np.mean(high[-length:] - low[-length:]))
    
    def _calculate_atr_series(self, high: np.ndarray, low: np.ndarray, 
                            close: np.ndarray, length: int) -> np.ndarray:
        """Calculate ATR series for time series analysis"""
        tr_values = np.zeros(len(high))
        
        # First TR value
        tr_values[0] = high[0] - low[0]
        
        # Calculate True Range for each period
        for i in range(1, len(high)):
            tr1 = high[i] - low[i]
            tr2 = abs(high[i] - close[i-1])
            tr3 = abs(low[i] - close[i-1])
            tr_values[i] = max(tr1, tr2, tr3)
        
        return tr_values
    
    def _calculate_pi_channels(self, mean: float, atr_range: float) -> Dict[str, float]:
        """
        Calculate Pi-based channel boundaries
        
        Mathematical Formula (EXACT):
        Inner: mean  (atr_range *  * 1.0)      = mean  (atr * 3.14159...)
        Outer: mean  (atr_range *  * 2.415)    = mean  (atr * 7.58858...)
        
        Args:
            mean: Composite mean value
            atr_range: ATR range for channel calculation
            
        Returns:
            Dict[str, float]: Channel boundaries with mathematical precision
        """
        try:
            # Pi-based multipliers (IMMUTABLE CONSTANTS)
            inner_distance = atr_range * self.PI * self.config.pi_inner_multiplier
            outer_distance = atr_range * self.PI * self.config.pi_outer_multiplier
            
            return {
                'upper_inner': float(mean + inner_distance),
                'lower_inner': float(mean - inner_distance),
                'upper_outer': float(mean + outer_distance),
                'lower_outer': float(mean - outer_distance)
            }
            
        except Exception as e:
            self.logger.error(f"Pi channel calculation failed: {e}")
            # Fallback to simple ATR channels
            return {
                'upper_inner': float(mean + atr_range),
                'lower_inner': float(mean - atr_range),
                'upper_outer': float(mean + 2 * atr_range),
                'lower_outer': float(mean - 2 * atr_range)
            }
    
    def _calculate_z_score(self, current_price: float, mean: float, 
                          price_series: np.ndarray, period: int) -> float:
        """
        Calculate Z-score for statistical analysis
        
        Formula: Z = (current_price - mean) / standard_deviation
        
        Args:
            current_price: Current market price
            mean: Statistical mean (composite mean)
            price_series: Historical price series
            period: Period for standard deviation calculation
            
        Returns:
            float: Z-score with statistical precision
        """
        try:
            # Calculate standard deviation over the period
            recent_prices = price_series[-period:]
            std_dev = np.std(recent_prices, ddof=1)  # Sample standard deviation
            
            if std_dev == 0:
                return 0.0
            
            z_score = (current_price - mean) / std_dev
            return float(z_score)
            
        except Exception as e:
            self.logger.error(f"Z-score calculation failed: {e}")
            return 0.0
    
    def _calculate_mdx(self, price_series: np.ndarray, mean: float, atr_range: float) -> float:
        """
        Calculate Mean Deviation Index (MDX)
        
        Measures current deviation relative to ATR-normalized range
        
        Args:
            price_series: Price data series
            mean: Composite mean value
            atr_range: ATR range for normalization
            
        Returns:
            float: MDX value (deviation index)
        """
        try:
            current_price = price_series[-1]
            deviation = abs(current_price - mean)
            
            if atr_range > 0:
                mdx = deviation / atr_range
            else:
                mdx = 0.0
                
            return float(mdx)
            
        except Exception as e:
            self.logger.error(f"MDX calculation failed: {e}")
            return 0.0
    
    def _generate_signal(self, current_price: float, mean: float, 
                        z_score: float, channels: Dict[str, float]) -> str:
        """
        Generate mean reversion signal based on statistical analysis
        
        Args:
            current_price: Current market price
            mean: Composite mean value
            z_score: Statistical Z-score
            channels: Pi-based channel boundaries
            
        Returns:
            str: Signal direction ('BUY', 'SELL', 'NEUTRAL')
        """
        try:
            # Signal logic based on statistical significance and channel position
            
            # Extreme oversold condition (potential BUY)
            if (z_score < -self.config.z_score_extreme and 
                current_price < channels['lower_inner']):
                return "BUY"
            
            # Significant oversold condition (potential BUY)
            elif (z_score < -self.config.z_score_significant and 
                  current_price < channels['lower_inner']):
                return "BUY"
            
            # Extreme overbought condition (potential SELL)
            elif (z_score > self.config.z_score_extreme and 
                  current_price > channels['upper_inner']):
                return "SELL"
            
            # Significant overbought condition (potential SELL)  
            elif (z_score > self.config.z_score_significant and 
                  current_price > channels['upper_inner']):
                return "SELL"
            
            # Price near mean - neutral condition
            else:
                return "NEUTRAL"
                
        except Exception as e:
            self.logger.error(f"Signal generation failed: {e}")
            return "NEUTRAL"
    
    def get_real_time_market_data(self, ticker: str) -> Optional[Dict[str, Any]]:
        """
        Get real-time market data via centralized pipeline
        
        Args:
            ticker: Stock symbol
            
        Returns:
            Real-time market data or None if unavailable
        """
        try:
            # Use centralized pipeline - NO DIRECT MCP CALLS
            from pipeline_fix import AgentDataInterface
            
            # Get data through pipeline
            bars_df = AgentDataInterface.get_bars_data(ticker, "1")
            comprehensive_data = AgentDataInterface.get_comprehensive_data(ticker)
            
            if bars_df.empty:
                return None
            
            # Convert to trading system format
            bars = bars_df.to_dict('records') if not bars_df.empty else []
            current_quote = comprehensive_data.get('options', {}).get('data', {})
            
            # Extract OHLCV data
            high_prices = [float(bar.get('h', 0)) for bar in bars]
            low_prices = [float(bar.get('l', 0)) for bar in bars]
            close_prices = [float(bar.get('c', 0)) for bar in bars]
            volume_data = [int(bar.get('v', 0)) for bar in bars]
            
            # Add current candle if available
            if current_quote and 'last_price' in current_quote:
                current_price = float(current_quote['last_price'])
                high_prices.append(current_price)
                low_prices.append(current_price) 
                close_prices.append(current_price)
                volume_data.append(int(current_quote.get('volume', 0)))
                
                self.logger.info(f"Using REAL-TIME Schwab MCP data for {ticker} mean reversion analysis")
                
                return {
                    'high': high_prices,
                    'low': low_prices,
                    'close': close_prices,
                    'volume': volume_data,
                    'current_price': close_prices[-1] if close_prices else 100.0,
                    'data_source': 'schwab_mcp_real_time',
                    'is_real_time': True
                }
            else:
                self.logger.warning(f"Schwab MCP unavailable for {ticker}, using provided data")
                return None
                
        except Exception as e:
            self.logger.warning(f"Failed to get real-time data for {ticker}: {e}")
            return None
    
    def enhance_with_real_time_data(self, task: AgentTask) -> AgentTask:
        """
        Enhance task with real-time Schwab MCP data if available
        
        Args:
            task: Original task
            
        Returns:
            Enhanced task with real-time data
        """
        try:
            market_data = task.inputs.get('market_data', {})
            ticker = market_data.get('ticker', 'UNKNOWN')
            
            # Try to get real-time data
            real_time_data = self.get_real_time_market_data(ticker)
            
            if real_time_data:
                # Replace market data with real-time version
                enhanced_inputs = task.inputs.copy()
                enhanced_inputs['market_data'] = real_time_data
                
                # Create enhanced task
                enhanced_task = AgentTask(
                    task_id=task.task_id + "_mcp_enhanced",
                    task_type=task.task_type,
                    agent_type=task.agent_type,
                    priority=task.priority,
                    inputs=enhanced_inputs,
                    workflow_file=task.workflow_file,
                    quality_standards=task.quality_standards,
                    performance_targets=task.performance_targets,
                    dependencies=task.dependencies,
                    training_data_tags=task.training_data_tags + ['schwab_mcp_real_time'],
                    timestamp=task.timestamp
                )
                
                self.logger.info(f"Enhanced {ticker} analysis with real-time Schwab MCP data")
                return enhanced_task
            else:
                self.logger.info(f"Using original data for {ticker} analysis")
                return task
                
        except Exception as e:
            self.logger.error(f"Failed to enhance task with real-time data: {e}")
            return task