#!/usr/bin/env python3
"""
Options Flow Decoder Agent - FIXED
Gets data through Data Ingestion Agent like all other agents
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, date
import sys
from pathlib import Path

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from agents.agent_base import BaseAgent

@dataclass
class OptionsFlowConfig:
    """Configuration for options flow analysis"""
    timeout_ms: int = 5000
    precision_threshold: float = 0.001
    enable_validation: bool = True
    volume_threshold: float = 1000.0
    oi_threshold: float = 500.0

class OptionsFlowDecoderAgent(BaseAgent):
    """
    Options Flow Decoder Agent
    Gets data through Data Ingestion Agent like all other agents
    """
    
    task_id = "ARMY-03"
    
    def __init__(self, agent_id="options_flow_decoder_agent", config=None):
        """Initialize with optional config"""
        super().__init__(agent_id)
        
        # Use provided config or default
        self.config = config or OptionsFlowConfig()
        
        # Initialize Data Ingestion Agent for proper data access
        try:
            from agents.data_ingestion_agent import LiveDataGatewayAgent
            self.data_agent = LiveDataGatewayAgent()
            self.has_data_agent = True
            self.logger.info("Options Flow Decoder Agent: Using Data Ingestion Agent")
        except ImportError:
            self.logger.warning("Data Ingestion Agent not available - using fallback")
            self.has_data_agent = False
        
    async def process_options_flow(self, symbol: str = None) -> Dict[str, Any]:
        """Main processing: Decode options flow using proper data access"""
        
        try:
            # Get data through Data Ingestion Agent (proper pattern)
            if self.has_data_agent:
                market_data = await self.data_agent.get_market_data(symbol)
                options_data = await self.data_agent.get_options_chain(symbol)
            else:
                # Fallback for testing
                market_data = self._generate_fallback_market_data(symbol)
                options_data = self._generate_fallback_options_data(symbol)
            
            # Extract key metrics
            underlying_price = market_data.get('price', 100.0)
            volume = market_data.get('volume', 1000000)
            
            # Process options flow
            flow_analysis = self._analyze_options_flow(options_data, underlying_price)
            
            # Calculate directional bias
            bias_analysis = self._calculate_directional_bias(flow_analysis, volume)
            
            return {
                'timestamp': datetime.now().isoformat(),
                'symbol': symbol or 'UNKNOWN',
                'underlying_price': underlying_price,
                'options_flow_analysis': flow_analysis,
                'directional_bias': bias_analysis,
                'data_quality_score': self._calculate_data_quality(market_data, options_data),
                'agent_id': self.agent_id,
                'task_id': self.task_id
            }
            
        except Exception as e:
            self.logger.error(f"Options flow processing failed: {e}")
            return self._generate_error_response(str(e))
    
    def _analyze_options_flow(self, options_data: Dict[str, Any], underlying_price: float) -> Dict[str, Any]:
        """Analyze options flow patterns"""
        
        try:
            # Extract call/put data
            calls_volume = options_data.get('total_calls_volume', 0)
            puts_volume = options_data.get('total_puts_volume', 0)
            calls_oi = options_data.get('total_calls_oi', 0)
            puts_oi = options_data.get('total_puts_oi', 0)
            
            # Calculate flow ratios
            total_volume = calls_volume + puts_volume
            total_oi = calls_oi + puts_oi
            
            if total_volume > 0:
                call_put_ratio = calls_volume / puts_volume if puts_volume > 0 else float('inf')
                volume_bias = (calls_volume - puts_volume) / total_volume
            else:
                call_put_ratio = 1.0
                volume_bias = 0.0
            
            if total_oi > 0:
                oi_ratio = calls_oi / puts_oi if puts_oi > 0 else float('inf')
                oi_bias = (calls_oi - puts_oi) / total_oi
            else:
                oi_ratio = 1.0
                oi_bias = 0.0
            
            return {
                'call_put_volume_ratio': call_put_ratio,
                'call_put_oi_ratio': oi_ratio,
                'volume_bias': volume_bias,
                'oi_bias': oi_bias,
                'total_volume': total_volume,
                'total_oi': total_oi,
                'calls_volume': calls_volume,
                'puts_volume': puts_volume
            }
            
        except Exception as e:
            self.logger.error(f"Options flow analysis failed: {e}")
            return {}
    
    def _calculate_directional_bias(self, flow_analysis: Dict[str, Any], volume: float) -> Dict[str, Any]:
        """Calculate directional bias from flow analysis"""
        
        try:
            volume_bias = flow_analysis.get('volume_bias', 0.0)
            oi_bias = flow_analysis.get('oi_bias', 0.0)
            
            # Weighted bias calculation
            combined_bias = (volume_bias * 0.6) + (oi_bias * 0.4)
            
            # Determine direction and strength
            if combined_bias > 0.2:
                direction = 'BULLISH'
                strength = 'STRONG' if combined_bias > 0.5 else 'MODERATE'
            elif combined_bias < -0.2:
                direction = 'BEARISH'
                strength = 'STRONG' if combined_bias < -0.5 else 'MODERATE'
            else:
                direction = 'NEUTRAL'
                strength = 'WEAK'
            
            # Confidence based on volume
            confidence = min(100.0, max(0.0, (volume / 1000000) * 50 + 30))
            
            return {
                'direction': direction,
                'strength': strength,
                'bias_score': combined_bias,
                'confidence': confidence,
                'volume_component': volume_bias,
                'oi_component': oi_bias
            }
            
        except Exception as e:
            self.logger.error(f"Directional bias calculation failed: {e}")
            return {'direction': 'NEUTRAL', 'strength': 'WEAK', 'bias_score': 0.0}

    
    def _calculate_data_quality(self, market_data: Dict[str, Any], options_data: Dict[str, Any]) -> float:
        """Calculate data quality score"""
        
        try:
            quality_score = 100.0
            
            # Check market data completeness
            required_market_fields = ['price', 'volume', 'timestamp']
            missing_market = sum(1 for field in required_market_fields if field not in market_data)
            quality_score -= (missing_market * 15)
            
            # Check options data completeness
            required_options_fields = ['total_calls_volume', 'total_puts_volume']
            missing_options = sum(1 for field in required_options_fields if field not in options_data)
            quality_score -= (missing_options * 20)
            
            # Volume threshold check
            volume = market_data.get('volume', 0)
            if volume < self.config.volume_threshold:
                quality_score -= 25
            
            return max(0.0, min(100.0, quality_score))
            
        except Exception:
            return 50.0  # Default quality for errors
    
    def _generate_fallback_market_data(self, symbol: str) -> Dict[str, Any]:
        """Generate fallback market data for testing"""
        
        return {
            'symbol': symbol or 'TEST',
            'price': 100.0 + np.random.uniform(-5, 5),
            'volume': int(np.random.uniform(500000, 2000000)),
            'timestamp': datetime.now().isoformat(),
            'bid': 99.95,
            'ask': 100.05
        }
    
    def _generate_fallback_options_data(self, symbol: str) -> Dict[str, Any]:
        """Generate fallback options data for testing"""
        
        return {
            'symbol': symbol or 'TEST',
            'total_calls_volume': int(np.random.uniform(10000, 50000)),
            'total_puts_volume': int(np.random.uniform(8000, 40000)),
            'total_calls_oi': int(np.random.uniform(100000, 500000)),
            'total_puts_oi': int(np.random.uniform(80000, 400000)),
            'timestamp': datetime.now().isoformat()
        }
    
    def _generate_error_response(self, error_msg: str) -> Dict[str, Any]:
        """Generate standardized error response"""
        
        return {
            'timestamp': datetime.now().isoformat(),
            'symbol': 'ERROR',
            'error': error_msg,
            'agent_id': self.agent_id,
            'task_id': self.task_id,
            'status': 'FAILED'
        }

    
    async def execute_task(self, symbol: str = None, **kwargs) -> Dict[str, Any]:
        """Execute main task - required by BaseAgent"""
        return await self.process_options_flow(symbol)
    
    def validate_inputs(self, **kwargs) -> bool:
        """Validate inputs - required by BaseAgent"""
        return True
    
    def validate_outputs(self, result: Dict[str, Any]) -> bool:
        """Validate outputs - required by BaseAgent"""
        required_fields = ['timestamp', 'symbol', 'agent_id', 'task_id']
        return all(field in result for field in required_fields)
