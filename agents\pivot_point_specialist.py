#!/usr/bin/env python3
"""
Pivot Point Specialist Agent
Multi-Method Pivot Calculations + Support/Resistance Analysis

Implementation Date: 2025-06-24
Mathematical Foundation: Traditional + Fibonacci + Camarilla Pivots
Precision Standard: Multi-timeframe analysis (Daily/Weekly/Monthly)
Performance Budget: 5 seconds execution
"""

import math
import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from datetime import datetime

from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus

logger = logging.getLogger(__name__)

@dataclass
class PivotConfig:
    """Configuration for Pivot Point Specialist Agent"""
    # Performance parameters
    timeout_ms: int = 5000
    precision_tolerance: float = 1e-10
    
    # Pivot calculation parameters
    confluence_tolerance_pct: float = 0.2  # 0.2% tolerance for level confluence
    
    # Fibonacci ratios (IMMUTABLE CONSTANTS)
    fib_382: float = 0.382
    fib_618: float = 0.618
    fib_1000: float = 1.000
    
    # Camarilla ratios (IMMUTABLE CONSTANTS)
    cam_ratio_1: float = 1.1/12  # 0.09166...
    cam_ratio_2: float = 1.1/6   # 0.18333...
    cam_ratio_3: float = 1.1/4   # 0.27500...
    cam_ratio_4: float = 1.1/2   # 0.55000...
    
    # Breakout probability parameters
    volume_breakout_multiplier: float = 1.8  # Volume confirmation for breakouts
    proximity_threshold_pct: float = 0.1     # 0.1% proximity to level

@dataclass
class PivotLevel:
    """Data structure for individual pivot level"""
    level_id: str
    level_type: str  # 'pivot', 'resistance', 'support'
    method: str      # 'traditional', 'fibonacci', 'camarilla'
    timeframe: str   # 'daily', 'weekly', 'monthly'
    value: float
    strength: int    # Number of methods agreeing on this level
    breakout_probability: float

class PivotPointSpecialist(BaseAgent):
    """
    Pivot Point Specialist Agent
    
    Implements mathematical precision pivot analysis using:
    - Traditional pivot point calculations
    - Fibonacci pivot point calculations
    - Camarilla pivot point calculations
    - Multi-timeframe analysis (Daily/Weekly/Monthly)
    - Confluence detection algorithms
    - Breakout probability calculations
    
    Mathematical Requirements:
    - Multi-method calculations with exact formulas
    - Confluence detection within 0.2% tolerance
    - Breakout probability with volume confirmation
    - Support/resistance level validation
    """
    
    def __init__(self, config: Optional[PivotConfig] = None):
        super().__init__(agent_id="pivot_point_specialist")
        self.config = config or PivotConfig()
        self.logger = logger
        
        # Pivot levels tracking
        self.pivot_levels: List[PivotLevel] = []
        self.confluence_zones: List[Dict[str, Any]] = []
        
        # Performance tracking
        self.calculation_metrics = {}
        self.breakout_accuracy_metrics = {}
        
        self.logger.info("Pivot Point Specialist Agent initialized")
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """
        Validate task inputs for pivot point analysis
        
        Args:
            task: AgentTask with market data inputs
            
        Returns:
            bool: True if inputs valid for pivot calculations
        """
        try:
            inputs = task.inputs
            
            # Required fields validation
            if 'market_data' not in inputs:
                self.logger.error("Missing required field: market_data")
                return False
            
            market_data = inputs['market_data']
            
            # Market data structure validation
            required_fields = ['high', 'low', 'close', 'volume']
            for field in required_fields:
                if field not in market_data:
                    self.logger.error(f"Missing market data field: {field}")
                    return False
            
            # Multi-timeframe data validation
            if 'timeframe_data' in inputs:
                timeframe_data = inputs['timeframe_data']
                for timeframe in ['daily', 'weekly', 'monthly']:
                    if timeframe in timeframe_data:
                        tf_data = timeframe_data[timeframe]
                        for field in required_fields:
                            if field not in tf_data:
                                self.logger.error(f"Missing {timeframe} {field} data")
                                return False
            
            # Minimum data length validation
            min_length = 2  # Need at least previous period for pivot calculation
            for field in required_fields:
                if len(market_data[field]) < min_length:
                    self.logger.error(f"Insufficient data length for {field}: {len(market_data[field])} < {min_length}")
                    return False
            
            # Numerical validation
            for field in required_fields:
                data_array = np.array(market_data[field])
                if np.any(np.isnan(data_array)) or np.any(np.isinf(data_array)):
                    self.logger.error(f"Invalid numerical data in {field}")
                    return False
                
                if field != 'volume' and np.any(data_array <= 0):
                    self.logger.error(f"Non-positive values in {field}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Input validation failed: {e}")
            return False
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """
        Validate outputs meet pivot analysis standards
        
        Args:
            outputs: Task execution outputs
            
        Returns:
            Dict[str, float]: Quality metrics for validation
        """
        quality_metrics = {}
        
        try:
            # Required output fields
            required_fields = [
                'traditional_pivot', 'traditional_r1', 'traditional_r2',
                'traditional_s1', 'traditional_s2', 'fibonacci_levels',
                'camarilla_levels', 'nearest_resistance', 'nearest_support',
                'level_confluence', 'breakout_probability', 'pivot_signal'
            ]
            
            completeness = sum(1 for field in required_fields if field in outputs) / len(required_fields)
            quality_metrics['completeness'] = completeness
            
            # Mathematical precision validation
            precision_score = 1.0
            for field in ['traditional_pivot', 'traditional_r1', 'traditional_r2', 'traditional_s1', 'traditional_s2']:
                if field in outputs and isinstance(outputs[field], (int, float)):
                    value = outputs[field]
                    if math.isnan(value) or math.isinf(value):
                        precision_score = 0.0
                        break
            
            quality_metrics['mathematical_precision'] = precision_score
            
            # Level relationship validation (R2 > R1 > Pivot > S1 > S2)
            if all(field in outputs for field in ['traditional_r2', 'traditional_r1', 'traditional_pivot', 'traditional_s1', 'traditional_s2']):
                levels_ordered = (
                    outputs['traditional_r2'] > outputs['traditional_r1'] > 
                    outputs['traditional_pivot'] > outputs['traditional_s1'] > 
                    outputs['traditional_s2']
                )
                quality_metrics['level_logic'] = 1.0 if levels_ordered else 0.0
            else:
                quality_metrics['level_logic'] = 0.0
            
            # Breakout probability validation (0-100% range)
            if 'breakout_probability' in outputs:
                prob = outputs['breakout_probability']
                if 0.0 <= prob <= 100.0:
                    quality_metrics['probability_bounds'] = 1.0
                else:
                    quality_metrics['probability_bounds'] = 0.0
            else:
                quality_metrics['probability_bounds'] = 0.0
            
            # Confluence validation
            if 'level_confluence' in outputs:
                confluence = outputs['level_confluence']
                if isinstance(confluence, list):
                    quality_metrics['confluence_analysis'] = 1.0
                else:
                    quality_metrics['confluence_analysis'] = 0.0
            else:
                quality_metrics['confluence_analysis'] = 0.0
            
            # Overall quality score
            avg_quality = sum(quality_metrics.values()) / len(quality_metrics)
            quality_metrics['overall_quality'] = avg_quality
            
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"Output validation failed: {e}")
            return {'error': 0.0, 'overall_quality': 0.0}
    
    def execute_task(self, task: AgentTask) -> AgentResult:
        """
        Execute pivot point analysis with multi-method calculations
        
        Args:
            task: AgentTask containing market data
            
        Returns:
            AgentResult: Complete pivot point analysis
        """
        start_time = time.time()
        
        try:
            market_data = task.inputs['market_data']
            timeframe_data = task.inputs.get('timeframe_data', {})
            
            # Convert to numpy arrays for mathematical operations
            high = np.array(market_data['high'], dtype=np.float64)
            low = np.array(market_data['low'], dtype=np.float64)
            close = np.array(market_data['close'], dtype=np.float64)
            volume = np.array(market_data['volume'], dtype=np.float64)
            
            current_price = close[-1]
            
            # Step 1: Calculate Traditional Pivot Points
            traditional_pivots = self._calculate_traditional_pivots(high, low, close)
            
            # Step 2: Calculate Fibonacci Pivot Points
            fibonacci_pivots = self._calculate_fibonacci_pivots(
                traditional_pivots['pivot'], high[-2], low[-2]
            )
            
            # Step 3: Calculate Camarilla Pivot Points
            camarilla_pivots = self._calculate_camarilla_pivots(close[-2], high[-2], low[-2])
            
            # Step 4: Calculate Multi-timeframe Pivots
            weekly_pivots = {}
            monthly_pivots = {}
            
            if 'weekly' in timeframe_data:
                weekly_data = timeframe_data['weekly']
                weekly_pivots = self._calculate_traditional_pivots(
                    np.array(weekly_data['high']),
                    np.array(weekly_data['low']),
                    np.array(weekly_data['close'])
                )
            
            if 'monthly' in timeframe_data:
                monthly_data = timeframe_data['monthly']
                monthly_pivots = self._calculate_traditional_pivots(
                    np.array(monthly_data['high']),
                    np.array(monthly_data['low']),
                    np.array(monthly_data['close'])
                )
            
            # Step 5: Find nearest support and resistance levels
            nearest_resistance, nearest_support = self._find_nearest_levels(
                current_price, traditional_pivots, fibonacci_pivots, camarilla_pivots
            )
            
            # Step 6: Detect level confluence
            level_confluence = self._detect_level_confluence(
                traditional_pivots, fibonacci_pivots, camarilla_pivots,
                weekly_pivots, monthly_pivots
            )
            
            # Step 7: Calculate breakout probability
            breakout_probability = self._calculate_breakout_probability(
                current_price, volume, nearest_resistance, nearest_support
            )
            
            # Step 8: Generate pivot signal
            pivot_signal = self._generate_pivot_signal(
                current_price, nearest_resistance, nearest_support, breakout_probability
            )
            
            # Compile results
            outputs = {
                'traditional_pivot': float(traditional_pivots['pivot']),
                'traditional_r1': float(traditional_pivots['r1']),
                'traditional_r2': float(traditional_pivots['r2']),
                'traditional_s1': float(traditional_pivots['s1']),
                'traditional_s2': float(traditional_pivots['s2']),
                'fibonacci_levels': {k: float(v) for k, v in fibonacci_pivots.items()},
                'camarilla_levels': {k: float(v) for k, v in camarilla_pivots.items()},
                'weekly_pivots': {k: float(v) for k, v in weekly_pivots.items()},
                'monthly_pivots': {k: float(v) for k, v in monthly_pivots.items()},
                'nearest_resistance': float(nearest_resistance) if nearest_resistance else 0.0,
                'nearest_support': float(nearest_support) if nearest_support else 0.0,
                'level_confluence': level_confluence,
                'breakout_probability': float(breakout_probability),
                'pivot_signal': pivot_signal,
                'current_price': float(current_price),
                'total_levels_detected': len(level_confluence),
                'mathematical_validation': True,
                'calculation_timestamp': datetime.now().isoformat()
            }
            
            execution_time = time.time() - start_time
            
            # Performance validation
            if execution_time > (self.config.timeout_ms / 1000.0):
                self.logger.warning(f"Execution time {execution_time:.3f}s exceeded budget {self.config.timeout_ms/1000.0}s")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                outputs=outputs,
                execution_time=execution_time,
                quality_metrics={}  # Will be populated by validate_outputs
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Pivot point analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )    
    def _calculate_traditional_pivots(self, high: np.ndarray, low: np.ndarray, 
                                    close: np.ndarray) -> Dict[str, float]:
        """
        Calculate Traditional Pivot Points
        
        Mathematical Formulas (EXACT):
        Pivot = (High + Low + Close) / 3
        R1 = (2 * Pivot) - Low
        S1 = (2 * Pivot) - High
        R2 = Pivot + (High - Low)
        S2 = Pivot - (High - Low)
        
        Args:
            high: High price array
            low: Low price array
            close: Close price array
            
        Returns:
            Dict[str, float]: Traditional pivot levels
        """
        try:
            # Use previous period's data for pivot calculation
            prev_high = high[-2]
            prev_low = low[-2]
            prev_close = close[-2]
            
            # Traditional Pivot Point formula
            pivot = (prev_high + prev_low + prev_close) / 3.0
            
            # Resistance levels
            r1 = (2.0 * pivot) - prev_low
            r2 = pivot + (prev_high - prev_low)
            
            # Support levels
            s1 = (2.0 * pivot) - prev_high
            s2 = pivot - (prev_high - prev_low)
            
            return {
                'pivot': float(pivot),
                'r1': float(r1),
                'r2': float(r2),
                's1': float(s1),
                's2': float(s2)
            }
            
        except Exception as e:
            self.logger.error(f"Traditional pivot calculation failed: {e}")
            return {'pivot': 0.0, 'r1': 0.0, 'r2': 0.0, 's1': 0.0, 's2': 0.0}
    
    def _calculate_fibonacci_pivots(self, pivot: float, prev_high: float, 
                                  prev_low: float) -> Dict[str, float]:
        """
        Calculate Fibonacci Pivot Points
        
        Mathematical Formulas (EXACT):
        Range = Previous High - Previous Low
        R1 = Pivot + 0.382 * Range
        R2 = Pivot + 0.618 * Range
        R3 = Pivot + 1.000 * Range
        S1 = Pivot - 0.382 * Range
        S2 = Pivot - 0.618 * Range
        S3 = Pivot - 1.000 * Range
        
        Args:
            pivot: Traditional pivot point
            prev_high: Previous period high
            prev_low: Previous period low
            
        Returns:
            Dict[str, float]: Fibonacci pivot levels
        """
        try:
            # Calculate range
            range_hl = prev_high - prev_low
            
            # Fibonacci resistance levels
            fib_r1 = pivot + (self.config.fib_382 * range_hl)
            fib_r2 = pivot + (self.config.fib_618 * range_hl)
            fib_r3 = pivot + (self.config.fib_1000 * range_hl)
            
            # Fibonacci support levels
            fib_s1 = pivot - (self.config.fib_382 * range_hl)
            fib_s2 = pivot - (self.config.fib_618 * range_hl)
            fib_s3 = pivot - (self.config.fib_1000 * range_hl)
            
            return {
                'fib_r1': float(fib_r1),
                'fib_r2': float(fib_r2),
                'fib_r3': float(fib_r3),
                'fib_s1': float(fib_s1),
                'fib_s2': float(fib_s2),
                'fib_s3': float(fib_s3)
            }
            
        except Exception as e:
            self.logger.error(f"Fibonacci pivot calculation failed: {e}")
            return {'fib_r1': 0.0, 'fib_r2': 0.0, 'fib_r3': 0.0, 
                   'fib_s1': 0.0, 'fib_s2': 0.0, 'fib_s3': 0.0}
    
    def _calculate_camarilla_pivots(self, prev_close: float, prev_high: float, 
                                  prev_low: float) -> Dict[str, float]:
        """
        Calculate Camarilla Pivot Points
        
        Mathematical Formulas (EXACT):
        Range = Previous High - Previous Low
        R1 = Close + (1.1/12) * Range
        R2 = Close + (1.1/6) * Range
        R3 = Close + (1.1/4) * Range
        R4 = Close + (1.1/2) * Range
        S1 = Close - (1.1/12) * Range
        S2 = Close - (1.1/6) * Range
        S3 = Close - (1.1/4) * Range
        S4 = Close - (1.1/2) * Range
        
        Args:
            prev_close: Previous period close
            prev_high: Previous period high
            prev_low: Previous period low
            
        Returns:
            Dict[str, float]: Camarilla pivot levels
        """
        try:
            # Calculate range
            range_hl = prev_high - prev_low
            
            # Camarilla resistance levels
            cam_r1 = prev_close + (self.config.cam_ratio_1 * range_hl)
            cam_r2 = prev_close + (self.config.cam_ratio_2 * range_hl)
            cam_r3 = prev_close + (self.config.cam_ratio_3 * range_hl)
            cam_r4 = prev_close + (self.config.cam_ratio_4 * range_hl)
            
            # Camarilla support levels
            cam_s1 = prev_close - (self.config.cam_ratio_1 * range_hl)
            cam_s2 = prev_close - (self.config.cam_ratio_2 * range_hl)
            cam_s3 = prev_close - (self.config.cam_ratio_3 * range_hl)
            cam_s4 = prev_close - (self.config.cam_ratio_4 * range_hl)
            
            return {
                'cam_r1': float(cam_r1),
                'cam_r2': float(cam_r2),
                'cam_r3': float(cam_r3),
                'cam_r4': float(cam_r4),
                'cam_s1': float(cam_s1),
                'cam_s2': float(cam_s2),
                'cam_s3': float(cam_s3),
                'cam_s4': float(cam_s4)
            }
            
        except Exception as e:
            self.logger.error(f"Camarilla pivot calculation failed: {e}")
            return {'cam_r1': 0.0, 'cam_r2': 0.0, 'cam_r3': 0.0, 'cam_r4': 0.0,
                   'cam_s1': 0.0, 'cam_s2': 0.0, 'cam_s3': 0.0, 'cam_s4': 0.0}
    
    def _find_nearest_levels(self, current_price: float,
                           traditional: Dict[str, float],
                           fibonacci: Dict[str, float],
                           camarilla: Dict[str, float]) -> Tuple[Optional[float], Optional[float]]:
        """
        Find nearest resistance and support levels from all methods
        
        Args:
            current_price: Current market price
            traditional: Traditional pivot levels
            fibonacci: Fibonacci pivot levels
            camarilla: Camarilla pivot levels
            
        Returns:
            Tuple[Optional[float], Optional[float]]: (nearest_resistance, nearest_support)
        """
        try:
            # Collect all levels
            all_levels = []
            
            # Traditional levels
            for key, value in traditional.items():
                if value > 0:
                    all_levels.append(value)
            
            # Fibonacci levels
            for key, value in fibonacci.items():
                if value > 0:
                    all_levels.append(value)
            
            # Camarilla levels
            for key, value in camarilla.items():
                if value > 0:
                    all_levels.append(value)
            
            # Find nearest resistance (above current price)
            resistance_levels = [level for level in all_levels if level > current_price]
            nearest_resistance = min(resistance_levels) if resistance_levels else None
            
            # Find nearest support (below current price)
            support_levels = [level for level in all_levels if level < current_price]
            nearest_support = max(support_levels) if support_levels else None
            
            return nearest_resistance, nearest_support
            
        except Exception as e:
            self.logger.error(f"Nearest level calculation failed: {e}")
            return None, None
    
    def _detect_level_confluence(self, traditional: Dict[str, float],
                               fibonacci: Dict[str, float],
                               camarilla: Dict[str, float],
                               weekly: Dict[str, float],
                               monthly: Dict[str, float]) -> List[float]:
        """
        Detect confluence zones where multiple methods agree
        
        Args:
            traditional: Traditional pivot levels
            fibonacci: Fibonacci pivot levels
            camarilla: Camarilla pivot levels
            weekly: Weekly pivot levels
            monthly: Monthly pivot levels
            
        Returns:
            List[float]: Confluence zone levels
        """
        try:
            # Collect all levels with their sources
            level_sources = []
            
            # Add traditional levels
            for key, value in traditional.items():
                if value > 0:
                    level_sources.append((value, 'traditional'))
            
            # Add fibonacci levels
            for key, value in fibonacci.items():
                if value > 0:
                    level_sources.append((value, 'fibonacci'))
            
            # Add camarilla levels
            for key, value in camarilla.items():
                if value > 0:
                    level_sources.append((value, 'camarilla'))
            
            # Add weekly levels
            for key, value in weekly.items():
                if value > 0:
                    level_sources.append((value, 'weekly'))
            
            # Add monthly levels
            for key, value in monthly.items():
                if value > 0:
                    level_sources.append((value, 'monthly'))
            
            # Sort levels by price
            level_sources.sort(key=lambda x: x[0])
            
            # Find confluence zones (levels within tolerance)
            confluence_zones = []
            tolerance = self.config.confluence_tolerance_pct / 100.0
            
            i = 0
            while i < len(level_sources):
                current_level = level_sources[i][0]
                cluster_levels = [level_sources[i]]
                
                # Find all levels within tolerance
                j = i + 1
                while j < len(level_sources):
                    next_level = level_sources[j][0]
                    if abs(next_level - current_level) / current_level <= tolerance:
                        cluster_levels.append(level_sources[j])
                        j += 1
                    else:
                        break
                
                # If multiple levels agree, it's a confluence zone
                if len(cluster_levels) >= 2:
                    cluster_prices = [level[0] for level in cluster_levels]
                    confluence_level = np.mean(cluster_prices)
                    confluence_zones.append(float(confluence_level))
                
                i = j if j > i + 1 else i + 1
            
            return confluence_zones
            
        except Exception as e:
            self.logger.error(f"Confluence detection failed: {e}")
            return []
    
    def _calculate_breakout_probability(self, current_price: float, volume: np.ndarray,
                                      nearest_resistance: Optional[float],
                                      nearest_support: Optional[float]) -> float:
        """
        Calculate breakout probability based on price proximity and volume
        
        Args:
            current_price: Current market price
            volume: Volume data array
            nearest_resistance: Nearest resistance level
            nearest_support: Nearest support level
            
        Returns:
            float: Breakout probability (0-100%)
        """
        try:
            # Base probability
            base_probability = 30.0
            
            # Volume factor
            avg_volume = np.mean(volume[-20:]) if len(volume) >= 20 else np.mean(volume)
            current_volume = volume[-1]
            volume_multiplier = current_volume / avg_volume
            
            # Volume confirmation boost
            if volume_multiplier > self.config.volume_breakout_multiplier:
                volume_boost = min((volume_multiplier - 1.0) * 20.0, 40.0)
            else:
                volume_boost = 0.0
            
            # Proximity factor
            proximity_boost = 0.0
            proximity_threshold = self.config.proximity_threshold_pct / 100.0
            
            if nearest_resistance:
                resistance_distance = abs(current_price - nearest_resistance) / current_price
                if resistance_distance <= proximity_threshold:
                    proximity_boost += 20.0
            
            if nearest_support:
                support_distance = abs(current_price - nearest_support) / current_price
                if support_distance <= proximity_threshold:
                    proximity_boost += 20.0
            
            # Calculate final probability
            total_probability = base_probability + volume_boost + proximity_boost
            final_probability = min(total_probability, 95.0)
            
            return float(final_probability)
            
        except Exception as e:
            self.logger.error(f"Breakout probability calculation failed: {e}")
            return 30.0
    
    def _generate_pivot_signal(self, current_price: float,
                             nearest_resistance: Optional[float],
                             nearest_support: Optional[float],
                             breakout_probability: float) -> str:
        """
        Generate pivot-based signal
        
        Args:
            current_price: Current market price
            nearest_resistance: Nearest resistance level
            nearest_support: Nearest support level
            breakout_probability: Breakout probability percentage
            
        Returns:
            str: Signal ('RESISTANCE', 'SUPPORT', 'NEUTRAL')
        """
        try:
            # High probability breakout scenario
            if breakout_probability > 70.0:
                if nearest_resistance and nearest_support:
                    resistance_distance = abs(current_price - nearest_resistance)
                    support_distance = abs(current_price - nearest_support)
                    
                    # Closer to resistance with high breakout probability
                    if resistance_distance < support_distance:
                        return "RESISTANCE"
                    # Closer to support with high breakout probability
                    else:
                        return "SUPPORT"
                
                elif nearest_resistance:
                    return "RESISTANCE"
                elif nearest_support:
                    return "SUPPORT"
            
            # Normal probability scenario
            elif nearest_resistance and nearest_support:
                resistance_distance = abs(current_price - nearest_resistance)
                support_distance = abs(current_price - nearest_support)
                
                # Within 0.2% of resistance level
                if resistance_distance / current_price <= 0.002:
                    return "RESISTANCE"
                # Within 0.2% of support level
                elif support_distance / current_price <= 0.002:
                    return "SUPPORT"
            
            return "NEUTRAL"
            
        except Exception as e:
            self.logger.error(f"Pivot signal generation failed: {e}")
            return "NEUTRAL"