#!/usr/bin/env python3
"""
Agnostic 60-Day Backtest System for CORE Trading System
Comprehensive backtesting that works with any ticker without hardcoded values
"""

import sys
import os
import json
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import asyncio

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

# Import CORE system components
from ultimate_orchestrator import ultimate_trading_pipeline
from agents.data_ingestion_agent import LiveDataGatewayAgent

@dataclass
class AgnosticBacktestConfig:
    """Configuration for agnostic backtesting"""
    ticker: str
    days: int = 60
    initial_capital: float = 100000.0
    commission_rate: float = 0.001
    slippage_bps: float = 2.0
    max_position_size: float = 0.1
    decisions_per_day: int = 4  # Every 6 hours
    risk_free_rate: float = 0.02  # 2% annual risk-free rate
    
    # Dynamic thresholds (no hardcoded values)
    confidence_threshold: float = 0.7  # Minimum confidence for execution
    ensemble_threshold: float = 0.65   # Minimum ensemble score for action

@dataclass
class BacktestTrade:
    """Individual trade record"""
    timestamp: datetime
    ticker: str
    action: str  # BUY, SELL, HOLD
    confidence: float
    ensemble_score: float
    price: float
    quantity: float
    commission: float
    slippage: float
    pnl: Optional[float] = None

@dataclass
class BacktestResults:
    """Comprehensive backtest results"""
    config: AgnosticBacktestConfig
    start_date: datetime
    end_date: datetime
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    calmar_ratio: float
    total_trades: int
    win_rate: float
    avg_trade_return: float
    profit_factor: float
    final_portfolio_value: float
    peak_portfolio_value: float
    total_commission: float
    total_slippage: float
    agent_zero_decisions: List[Dict[str, Any]]
    trades: List[BacktestTrade]

class AgnosticBacktester:
    """
    Agnostic 60-day backtesting system that works with any ticker
    No hardcoded values - all parameters are dynamically determined
    """
    
    def __init__(self, config: AgnosticBacktestConfig):
        self.config = config
        self.logger = self._setup_logging()
        
        # Portfolio state
        self.cash = config.initial_capital
        self.position = 0.0  # Number of shares
        self.portfolio_value = config.initial_capital
        self.peak_value = config.initial_capital
        self.max_drawdown = 0.0
        
        # Trading history
        self.trades: List[BacktestTrade] = []
        self.portfolio_history: List[Dict[str, Any]] = []
        self.agent_zero_decisions: List[Dict[str, Any]] = []
        
        # Data agent for market data
        self.data_agent = LiveDataGatewayAgent()
        
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger(f"AgnosticBacktest_{self.config.ticker}")
    
    def get_historical_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Get historical market data for the ticker"""
        try:
            # Use data agent to get real market data
            market_data = self.data_agent.get_market_data(self.config.ticker)
            
            if market_data and 'historical_data' in market_data:
                df = pd.DataFrame(market_data['historical_data'])
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp')
                
                # Filter by date range
                df = df[(df.index >= start_date) & (df.index <= end_date)]
                return df
            else:
                # Fallback: Generate synthetic data for testing
                self.logger.warning(f"No real data available for {self.config.ticker}, using synthetic data")
                return self._generate_synthetic_data(start_date, end_date)
                
        except Exception as e:
            self.logger.error(f"Failed to get historical data: {e}")
            return self._generate_synthetic_data(start_date, end_date)
    
    def _generate_synthetic_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Generate synthetic market data for testing purposes"""
        dates = pd.date_range(start=start_date, end=end_date, freq='1H')
        
        # Generate realistic price movement
        np.random.seed(hash(self.config.ticker) & 0x7FFFFFFF)
        initial_price = np.random.uniform(50, 500)
        
        # Generate price series with realistic volatility
        returns = np.random.normal(0, 0.02, len(dates))  # 2% hourly volatility
        prices = [initial_price]
        
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))
        
        # Generate volume data
        volumes = np.random.lognormal(15, 1, len(dates))  # Log-normal volume distribution
        
        df = pd.DataFrame({
            'open': prices,
            'high': [p * np.random.uniform(1.0, 1.02) for p in prices],
            'low': [p * np.random.uniform(0.98, 1.0) for p in prices],
            'close': prices,
            'volume': volumes
        }, index=dates)
        
        return df
    
    def calculate_position_size(self, price: float, confidence: float) -> float:
        """Calculate position size based on confidence and risk management"""
        # Dynamic position sizing based on confidence
        base_position_pct = self.config.max_position_size * confidence
        
        # Calculate maximum shares we can afford
        max_shares = (self.cash * base_position_pct) / price
        
        # Apply commission and slippage considerations
        commission_cost = max_shares * price * self.config.commission_rate
        slippage_cost = max_shares * price * (self.config.slippage_bps / 10000)
        
        # Adjust for transaction costs
        total_cost = max_shares * price + commission_cost + slippage_cost
        
        if total_cost > self.cash:
            max_shares = self.cash / (price * (1 + self.config.commission_rate + self.config.slippage_bps / 10000))
        
        return max_shares
    
    def execute_trade(self, timestamp: datetime, action: str, price: float, 
                     confidence: float, ensemble_score: float, 
                     agent_zero_decision: Dict[str, Any]) -> Optional[BacktestTrade]:
        """Execute a trade based on Agent Zero decision"""
        
        if action == "HOLD" or confidence < self.config.confidence_threshold:
            return None
        
        # Calculate position size
        if action == "BUY" and self.position == 0:
            quantity = self.calculate_position_size(price, confidence)
            if quantity < 1:  # Minimum 1 share
                return None
                
            # Execute buy order
            total_cost = quantity * price
            commission = total_cost * self.config.commission_rate
            slippage = total_cost * (self.config.slippage_bps / 10000)
            
            if total_cost + commission + slippage <= self.cash:
                self.cash -= (total_cost + commission + slippage)
                self.position = quantity
                
                trade = BacktestTrade(
                    timestamp=timestamp,
                    ticker=self.config.ticker,
                    action=action,
                    confidence=confidence,
                    ensemble_score=ensemble_score,
                    price=price,
                    quantity=quantity,
                    commission=commission,
                    slippage=slippage
                )
                
                self.trades.append(trade)
                return trade
        
        elif action == "SELL" and self.position > 0:
            # Execute sell order
            quantity = self.position
            total_proceeds = quantity * price
            commission = total_proceeds * self.config.commission_rate
            slippage = total_proceeds * (self.config.slippage_bps / 10000)
            
            self.cash += (total_proceeds - commission - slippage)
            
            # Calculate PnL
            buy_trade = None
            for trade in reversed(self.trades):
                if trade.action == "BUY" and trade.pnl is None:
                    buy_trade = trade
                    break
            
            pnl = 0.0
            if buy_trade:
                pnl = (price - buy_trade.price) * quantity - commission - slippage - buy_trade.commission - buy_trade.slippage
                buy_trade.pnl = pnl
            
            self.position = 0.0
            
            trade = BacktestTrade(
                timestamp=timestamp,
                ticker=self.config.ticker,
                action=action,
                confidence=confidence,
                ensemble_score=ensemble_score,
                price=price,
                quantity=quantity,
                commission=commission,
                slippage=slippage,
                pnl=pnl
            )
            
            self.trades.append(trade)
            return trade
        
        return None
    
    def update_portfolio_value(self, current_price: float, timestamp: datetime):
        """Update portfolio value and track performance"""
        position_value = self.position * current_price
        self.portfolio_value = self.cash + position_value
        
        # Track peak value and drawdown
        if self.portfolio_value > self.peak_value:
            self.peak_value = self.portfolio_value
        
        current_drawdown = (self.peak_value - self.portfolio_value) / self.peak_value
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        # Record portfolio history
        self.portfolio_history.append({
            'timestamp': timestamp,
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'position_value': position_value,
            'position_shares': self.position,
            'current_price': current_price,
            'drawdown': current_drawdown
        })

    def run_backtest(self) -> BacktestResults:
        """Run the complete 60-day agnostic backtest"""
        self.logger.info(f"Starting 60-day agnostic backtest for {self.config.ticker}")

        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=self.config.days)

        self.logger.info(f"Backtest period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        # Get historical data
        historical_data = self.get_historical_data(start_date, end_date)

        if historical_data.empty:
            raise ValueError(f"No historical data available for {self.config.ticker}")

        self.logger.info(f"Loaded {len(historical_data)} data points")

        # Generate decision timestamps (every 6 hours)
        decision_timestamps = []
        current_time = start_date
        while current_time <= end_date:
            decision_timestamps.append(current_time)
            current_time += timedelta(hours=6)  # 4 decisions per day

        self.logger.info(f"Will generate {len(decision_timestamps)} Agent Zero decisions")

        # Run backtest simulation
        for i, decision_time in enumerate(decision_timestamps):
            try:
                # Find closest market data point
                closest_data = historical_data.iloc[historical_data.index.get_indexer([decision_time], method='nearest')[0]]
                current_price = closest_data['close']

                # Run ultimate trading pipeline for this timestamp
                self.logger.info(f"Decision {i+1}/{len(decision_timestamps)}: {decision_time.strftime('%Y-%m-%d %H:%M')}")

                # Get Agent Zero decision
                pipeline_result = ultimate_trading_pipeline(self.config.ticker)

                # Extract Agent Zero intelligence
                agent_zero_intel = pipeline_result.get('agent_zero_intelligence', {})
                ensemble_intel = pipeline_result.get('ensemble_intelligence', {})

                # Determine action based on Agent Zero decision
                action = self._interpret_agent_zero_decision(agent_zero_intel, ensemble_intel)

                # Get confidence and ensemble score
                confidence = agent_zero_intel.get('confidence', 0.5)
                ensemble_score = ensemble_intel.get('ensemble_score', 50) / 100  # Convert to 0-1

                # Record Agent Zero decision
                decision_record = {
                    'timestamp': decision_time,
                    'price': current_price,
                    'action': action,
                    'confidence': confidence,
                    'ensemble_score': ensemble_score,
                    'agent_zero_decision': agent_zero_intel,
                    'ensemble_intelligence': ensemble_intel,
                    'pipeline_result': pipeline_result
                }
                self.agent_zero_decisions.append(decision_record)

                # Execute trade if conditions are met
                trade = self.execute_trade(
                    timestamp=decision_time,
                    action=action,
                    price=current_price,
                    confidence=confidence,
                    ensemble_score=ensemble_score,
                    agent_zero_decision=agent_zero_intel
                )

                if trade:
                    self.logger.info(f"   Executed {trade.action}: {trade.quantity:.2f} shares at ${trade.price:.2f}")

                # Update portfolio value
                self.update_portfolio_value(current_price, decision_time)

                # Log progress
                if (i + 1) % 10 == 0:
                    self.logger.info(f"   Progress: {i+1}/{len(decision_timestamps)} decisions completed")
                    self.logger.info(f"   Portfolio Value: ${self.portfolio_value:,.2f}")
                    self.logger.info(f"   Total Trades: {len(self.trades)}")

            except Exception as e:
                self.logger.error(f"Error processing decision at {decision_time}: {e}")
                continue

        # Calculate final results
        results = self._calculate_results(start_date, end_date)

        self.logger.info("Backtest completed successfully")
        self.logger.info(f"Final Portfolio Value: ${results.final_portfolio_value:,.2f}")
        self.logger.info(f"Total Return: {results.total_return:.2%}")
        self.logger.info(f"Sharpe Ratio: {results.sharpe_ratio:.2f}")
        self.logger.info(f"Max Drawdown: {results.max_drawdown:.2%}")
        self.logger.info(f"Total Trades: {results.total_trades}")
        self.logger.info(f"Win Rate: {results.win_rate:.2%}")

        return results

    def _interpret_agent_zero_decision(self, agent_zero_intel: Dict[str, Any],
                                     ensemble_intel: Dict[str, Any]) -> str:
        """Use Agent Zero's EXACT recommendation - no interpretation needed"""

        # Get Agent Zero's EXACT recommendation from ensemble intelligence
        agent_zero_recommendation = ensemble_intel.get('agent_zero_recommendation', 'HOLD')

        # DIRECT MAPPING from Agent Zero's actual output format
        if 'EXECUTE BUY' in agent_zero_recommendation:
            return "BUY"
        elif 'EXECUTE SELL' in agent_zero_recommendation:
            return "SELL"
        elif 'HOLD' in agent_zero_recommendation:
            return "HOLD"
        elif 'WAIT' in agent_zero_recommendation:
            return "HOLD"  # Wait = Hold in trading context
        else:
            # Fallback to Agent Zero's action if recommendation unclear
            agent_zero_action = agent_zero_intel.get('action', 'hold')
            ensemble_decision = ensemble_intel.get('final_decision', 'NEUTRAL')

            if agent_zero_action == 'execute':
                if ensemble_decision == 'BULLISH':
                    return "BUY"
                elif ensemble_decision == 'BEARISH':
                    return "SELL"
                else:
                    return "HOLD"
            else:
                return "HOLD"

    def _calculate_results(self, start_date: datetime, end_date: datetime) -> BacktestResults:
        """Calculate comprehensive backtest results"""

        # Basic metrics
        total_return = (self.portfolio_value - self.config.initial_capital) / self.config.initial_capital
        days_elapsed = (end_date - start_date).days
        annualized_return = (1 + total_return) ** (365 / days_elapsed) - 1

        # Calculate volatility from portfolio history
        if len(self.portfolio_history) > 1:
            portfolio_values = [p['portfolio_value'] for p in self.portfolio_history]
            returns = np.diff(portfolio_values) / portfolio_values[:-1]
            volatility = np.std(returns) * np.sqrt(252)  # Annualized
        else:
            volatility = 0.0

        # Sharpe ratio
        excess_return = annualized_return - self.config.risk_free_rate
        sharpe_ratio = excess_return / volatility if volatility > 0 else 0.0

        # Calmar ratio
        calmar_ratio = annualized_return / self.max_drawdown if self.max_drawdown > 0 else 0.0

        # Trade statistics
        total_trades = len([t for t in self.trades if t.pnl is not None])
        winning_trades = len([t for t in self.trades if t.pnl is not None and t.pnl > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0

        # Average trade return
        trade_returns = [t.pnl for t in self.trades if t.pnl is not None]
        avg_trade_return = np.mean(trade_returns) if trade_returns else 0.0

        # Profit factor
        gross_profit = sum([pnl for pnl in trade_returns if pnl > 0])
        gross_loss = abs(sum([pnl for pnl in trade_returns if pnl < 0]))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')

        # Commission and slippage totals
        total_commission = sum([t.commission for t in self.trades])
        total_slippage = sum([t.slippage for t in self.trades])

        return BacktestResults(
            config=self.config,
            start_date=start_date,
            end_date=end_date,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=self.max_drawdown,
            calmar_ratio=calmar_ratio,
            total_trades=total_trades,
            win_rate=win_rate,
            avg_trade_return=avg_trade_return,
            profit_factor=profit_factor,
            final_portfolio_value=self.portfolio_value,
            peak_portfolio_value=self.peak_value,
            total_commission=total_commission,
            total_slippage=total_slippage,
            agent_zero_decisions=self.agent_zero_decisions,
            trades=self.trades
        )

    def save_results(self, results: BacktestResults, output_dir: str = "backtest_results"):
        """Save backtest results to files"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename_base = f"{results.config.ticker}_{timestamp}"

        # Save summary results
        summary = {
            'ticker': results.config.ticker,
            'backtest_period': f"{results.start_date.strftime('%Y-%m-%d')} to {results.end_date.strftime('%Y-%m-%d')}",
            'total_return': f"{results.total_return:.2%}",
            'annualized_return': f"{results.annualized_return:.2%}",
            'volatility': f"{results.volatility:.2%}",
            'sharpe_ratio': f"{results.sharpe_ratio:.2f}",
            'max_drawdown': f"{results.max_drawdown:.2%}",
            'calmar_ratio': f"{results.calmar_ratio:.2f}",
            'total_trades': results.total_trades,
            'win_rate': f"{results.win_rate:.2%}",
            'avg_trade_return': f"${results.avg_trade_return:.2f}",
            'profit_factor': f"{results.profit_factor:.2f}",
            'final_portfolio_value': f"${results.final_portfolio_value:,.2f}",
            'total_commission': f"${results.total_commission:.2f}",
            'total_slippage': f"${results.total_slippage:.2f}"
        }

        with open(output_path / f"{filename_base}_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)

        # Save detailed trades
        trades_data = []
        for trade in results.trades:
            trades_data.append({
                'timestamp': trade.timestamp.isoformat(),
                'action': trade.action,
                'price': trade.price,
                'quantity': trade.quantity,
                'confidence': trade.confidence,
                'ensemble_score': trade.ensemble_score,
                'commission': trade.commission,
                'slippage': trade.slippage,
                'pnl': trade.pnl
            })

        with open(output_path / f"{filename_base}_trades.json", 'w') as f:
            json.dump(trades_data, f, indent=2)

        # Save Agent Zero decisions
        with open(output_path / f"{filename_base}_agent_zero_decisions.json", 'w') as f:
            json.dump(results.agent_zero_decisions, f, indent=2, default=str)

        self.logger.info(f"Results saved to {output_path}")
        return output_path / f"{filename_base}_summary.json"

def run_agnostic_backtest(ticker: str, days: int = 60, **kwargs) -> BacktestResults:
    """
    Run agnostic backtest for any ticker

    Args:
        ticker: Stock ticker symbol
        days: Number of days to backtest (default 60)
        **kwargs: Additional configuration parameters

    Returns:
        BacktestResults object with comprehensive results
    """

    # Create configuration
    config = AgnosticBacktestConfig(
        ticker=ticker.upper(),
        days=days,
        **kwargs
    )

    # Run backtest
    backtester = AgnosticBacktester(config)
    results = backtester.run_backtest()

    # Save results
    backtester.save_results(results)

    return results

def run_multi_ticker_backtest(tickers: List[str], days: int = 60, **kwargs) -> Dict[str, BacktestResults]:
    """
    Run agnostic backtest for multiple tickers

    Args:
        tickers: List of stock ticker symbols
        days: Number of days to backtest (default 60)
        **kwargs: Additional configuration parameters

    Returns:
        Dictionary mapping ticker to BacktestResults
    """

    results = {}

    for ticker in tickers:
        print(f"\n{'='*50}")
        print(f"Running backtest for {ticker}")
        print(f"{'='*50}")

        try:
            result = run_agnostic_backtest(ticker, days, **kwargs)
            results[ticker] = result

            print(f"✓ {ticker} backtest completed successfully")
            print(f"  Total Return: {result.total_return:.2%}")
            print(f"  Sharpe Ratio: {result.sharpe_ratio:.2f}")
            print(f"  Max Drawdown: {result.max_drawdown:.2%}")

        except Exception as e:
            print(f"✗ {ticker} backtest failed: {e}")
            results[ticker] = None

    return results

def generate_comparison_report(results: Dict[str, BacktestResults]) -> str:
    """Generate comparison report for multiple ticker backtests"""

    report = []
    report.append("AGNOSTIC 60-DAY BACKTEST COMPARISON REPORT")
    report.append("=" * 60)
    report.append("")

    # Summary table
    report.append(f"{'Ticker':<8} {'Return':<10} {'Sharpe':<8} {'Drawdown':<10} {'Trades':<8} {'Win Rate':<10}")
    report.append("-" * 60)

    for ticker, result in results.items():
        if result:
            report.append(f"{ticker:<8} {result.total_return:>9.2%} {result.sharpe_ratio:>7.2f} "
                         f"{result.max_drawdown:>9.2%} {result.total_trades:>7d} {result.win_rate:>9.2%}")
        else:
            report.append(f"{ticker:<8} {'FAILED':<10} {'N/A':<8} {'N/A':<10} {'N/A':<8} {'N/A':<10}")

    report.append("")

    # Best performers
    valid_results = {k: v for k, v in results.items() if v is not None}

    if valid_results:
        best_return = max(valid_results.items(), key=lambda x: x[1].total_return)
        best_sharpe = max(valid_results.items(), key=lambda x: x[1].sharpe_ratio)
        best_drawdown = min(valid_results.items(), key=lambda x: x[1].max_drawdown)

        report.append("BEST PERFORMERS:")
        report.append(f"  Highest Return: {best_return[0]} ({best_return[1].total_return:.2%})")
        report.append(f"  Best Sharpe:    {best_sharpe[0]} ({best_sharpe[1].sharpe_ratio:.2f})")
        report.append(f"  Lowest Drawdown: {best_drawdown[0]} ({best_drawdown[1].max_drawdown:.2%})")

    return "\n".join(report)

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Agnostic 60-Day Backtest System")
    parser.add_argument("--ticker", type=str, help="Single ticker to backtest")
    parser.add_argument("--tickers", nargs="+", help="Multiple tickers to backtest")
    parser.add_argument("--days", type=int, default=60, help="Number of days to backtest")
    parser.add_argument("--capital", type=float, default=100000.0, help="Initial capital")
    parser.add_argument("--max-position", type=float, default=0.1, help="Maximum position size (0.1 = 10%)")

    args = parser.parse_args()

    if args.ticker:
        # Single ticker backtest
        result = run_agnostic_backtest(
            ticker=args.ticker,
            days=args.days,
            initial_capital=args.capital,
            max_position_size=args.max_position
        )

        print(f"\nBacktest completed for {args.ticker}")
        print(f"Total Return: {result.total_return:.2%}")
        print(f"Sharpe Ratio: {result.sharpe_ratio:.2f}")
        print(f"Max Drawdown: {result.max_drawdown:.2%}")

    elif args.tickers:
        # Multi-ticker backtest
        results = run_multi_ticker_backtest(
            tickers=args.tickers,
            days=args.days,
            initial_capital=args.capital,
            max_position_size=args.max_position
        )

        # Generate comparison report
        report = generate_comparison_report(results)
        print(f"\n{report}")

    else:
        # Default: Run backtest on common tickers
        default_tickers = ["SPY", "AAPL", "MSFT", "TSLA", "QQQ"]
        print("No tickers specified. Running default backtest on:", default_tickers)

        results = run_multi_ticker_backtest(
            tickers=default_tickers,
            days=args.days,
            initial_capital=args.capital,
            max_position_size=args.max_position
        )

        report = generate_comparison_report(results)
        print(f"\n{report}")
