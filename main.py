#!/usr/bin/env python3
"""
CORE Trading System - Fixed Main Entry Point
=============================================
Properly integrated with Agent Zero and specialized agents
Ticker Agnostic - Supports any ticker symbol
"""

import sys
import os
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Add paths for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def get_trading_system() -> str:
    """Determine which trading system to use"""
    
    # Check for ultimate orchestrator (comprehensive)
    ultimate_path = current_dir / "ultimate_orchestrator.py"
    if ultimate_path.exists():
        return "ultimate"
    
    # Note: agent_orchestrator capabilities now consolidated into ultimate_orchestrator
    # Fallback to basic system
    return "basic"

def run_ultimate_orchestrator(ticker: str, **kwargs) -> Dict[str, Any]:
    """Run comprehensive ultimate orchestrator"""
    try:
        from ultimate_orchestrator import ultimate_trading_pipeline
        
        print(f"Running Ultimate Intelligence Pipeline for {ticker}")
        result = ultimate_trading_pipeline(ticker)
        
        # Extract Agent Zero decision if available
        agent_zero_decision = result.get('agent_zero_intelligence', {})
        
        return {
            'system': 'ultimate_orchestrator',
            'ticker': ticker,
            'timestamp': datetime.now().isoformat(),
            'comprehensive_analysis': result,
            'agent_zero_decision': agent_zero_decision,
            'signal_sources': ['b_series', 'anomalies', 'iv_dynamics', 'flow_physics', 'specialized_army'],
            'status': 'SUCCESS'
        }
        
    except Exception as e:
        print(f"Ultimate orchestrator error: {e}")
        return {
            'system': 'ultimate_orchestrator', 
            'ticker': ticker,
            'error': str(e),
            'status': 'FAILED'
        }

def run_agent_orchestrator(ticker: str, **kwargs) -> Dict[str, Any]:
    """Run consolidated orchestrator (integrated into ultimate_orchestrator)"""
    try:
        from ultimate_orchestrator import run_agent_orchestrator_mode
        
        print(f"Running Consolidated Agent Orchestrator for {ticker}")
        
        # Execute consolidated orchestrator mode
        result = run_agent_orchestrator_mode(ticker, **kwargs)
        async def run_analysis():
            return await orchestrator.process(request_data)
        
        # Execute
        result = asyncio.run(run_analysis())
        
        
        return result
        
    except Exception as e:
        print(f"Agent orchestrator error: {e}")
        return {
            'system': 'consolidated_orchestrator',
            'ticker': ticker, 
            'error': str(e),
            'status': 'FAILED'
        }

def run_basic_system(ticker: str, **kwargs) -> Dict[str, Any]:
    """Run basic flow detection system as fallback"""
    try:
        print(f"Running Basic Flow Detection System for {ticker}")
        print("WARNING: Using fallback system - specialized agents not available")
        
        # Import basic system components
        from analyzers import FlowPhysicsAnalyzer, VolumeAnalyzer, LiquidityAnalyzer, GEXAnalyzer
        from engine import ConfluenceEngine, SignalGenerator
        
        # Try to get API data
        try:
            from api.unified_api_gateway import get_api_gateway
            api_gateway = get_api_gateway()
            print("Using MCP API Gateway")
        except ImportError:
            from data.api_gateway import APIGateway
            api_gateway = APIGateway({})
            print("Using placeholder API Gateway")
        
        # Initialize analyzers
        flow_analyzer = FlowPhysicsAnalyzer({})
        volume_analyzer = VolumeAnalyzer({})
        liquidity_analyzer = LiquidityAnalyzer({})
        gex_analyzer = GEXAnalyzer({})
        
        # Initialize engines
        confluence_engine = ConfluenceEngine({})
        signal_generator = SignalGenerator({})
        
        # Mock data for basic system
        market_data = {
            'current_price': 150.0,
            'mtf_data': {'15m': None, '1h': None},
            'timestamp': datetime.now()
        }
        
        # Generate basic signal
        signal = signal_generator.generate_signal(
            ticker=ticker,
            confluence_result={'score': 0.5, 'factors': []},
            current_price=150.0,
            additional_context={}
        )
        
        return {
            'system': 'basic_flow_detection',
            'ticker': ticker,
            'timestamp': datetime.now().isoformat(),
            'signal': signal,
            'signal_sources': ['flow_physics', 'volume', 'liquidity', 'gex'],
            'warning': 'Basic system used - Agent Zero integration not available',
            'status': 'SUCCESS'
        }
        
    except Exception as e:
        print(f"Basic system error: {e}")
        return {
            'system': 'basic_flow_detection',
            'ticker': ticker,
            'error': str(e),
            'status': 'FAILED'
        }

def extract_market_context_from_analysis(analysis_result: Dict[str, Any]) -> Dict[str, Any]:
    """Extract and format market analysis for dynamic feed calculator"""
    
    # Try to extract analysis from comprehensive analysis
    comprehensive = analysis_result.get('comprehensive_analysis', {})
    
    # Default market context structure
    market_context = {
        'b_series_analysis': {
            'features': {},
            'confidence': 0.6,
            'pattern_strength': 0.5
        },
        'flow_analysis': {
            'momentum': 0.0,
            'direction': 'neutral',
            'strength': 0.5
        },
        'anomaly_analysis': {
            'anomaly_detected': False,
            'anomaly_score': 0.0
        },
        'iv_dynamics_analysis': {
            'iv_rank': 50.0,
            'iv_expansion': False,
            'volatility_regime': 'normal'
        },
        'market_regime': {
            'trend': 'sideways',
            'volatility': 'medium'
        }
    }
    
    # Extract from ultimate orchestrator results if available
    if comprehensive:
        # B-Series intelligence
        b_series = comprehensive.get('b_series_intelligence', {})
        if b_series:
            market_context['b_series_analysis']['confidence'] = b_series.get('confidence', 0.6)
            market_context['b_series_analysis']['pattern_strength'] = b_series.get('pattern_strength', 0.5)
            
        # Flow physics
        flow_physics = comprehensive.get('flow_physics', {})
        if flow_physics:
            market_context['flow_analysis']['momentum'] = flow_physics.get('momentum', 0.0)
            market_context['flow_analysis']['direction'] = flow_physics.get('direction', 'neutral')
            market_context['flow_analysis']['strength'] = flow_physics.get('strength', 0.5)
            
        # Anomaly detection
        anomalies = comprehensive.get('anomalies', {})
        if anomalies:
            market_context['anomaly_analysis']['anomaly_detected'] = anomalies.get('detected', False)
            market_context['anomaly_analysis']['anomaly_score'] = anomalies.get('score', 0.0)
            
        # IV dynamics
        iv_dynamics = comprehensive.get('iv_dynamics', {})
        if iv_dynamics:
            market_context['iv_dynamics_analysis']['iv_rank'] = iv_dynamics.get('iv_rank', 50.0)
            market_context['iv_dynamics_analysis']['iv_expansion'] = iv_dynamics.get('expansion', False)
            market_context['iv_dynamics_analysis']['volatility_regime'] = iv_dynamics.get('regime', 'normal')
            
        # Market regime
        regime = comprehensive.get('market_regime', {})
        if regime:
            market_context['market_regime']['trend'] = regime.get('trend', 'sideways')
            market_context['market_regime']['volatility'] = regime.get('volatility', 'medium')
    
    # Add analysis metadata
    market_context['analysis_source'] = analysis_result.get('system', 'unknown')
    market_context['timestamp'] = analysis_result.get('timestamp', datetime.now().isoformat())
    market_context['ticker'] = analysis_result.get('ticker', 'UNKNOWN')
    
    return market_context

def integrate_with_agent_zero(analysis_result: Dict[str, Any]) -> Dict[str, Any]:
    """Integrate analysis result with Agent Zero using dynamic feeds"""
    try:
        from agents.agent_zero import AgentZeroAdvisor
        from dynamic_feed_calculator import DynamicFeedCalculator
        
        print("Integrating with Agent Zero (Dynamic Feeds)...")
        agent_zero = AgentZeroAdvisor()
        
        # Extract signal data from analysis
        if analysis_result['system'] == 'ultimate_orchestrator':
            # Ultimate orchestrator already has Agent Zero integration
            agent_zero_decision = analysis_result.get('agent_zero_decision', {})
            if agent_zero_decision:
                print("Agent Zero decision already available from ultimate orchestrator")
                return analysis_result
        
        # DYNAMIC FEED IMPLEMENTATION
        # Extract market analysis from result to create dynamic context
        market_context = extract_market_context_from_analysis(analysis_result)
        
        # Use Dynamic Feed Calculator to convert analysis to Agent Zero inputs
        calculator = DynamicFeedCalculator()
        dynamic_inputs = calculator.calculate_all_dynamic_inputs(market_context)
        
        # Extract dynamic signal and math data
        signal_data = dynamic_inputs['signal_data']
        math_data = dynamic_inputs['math_data']
        full_market_context = dynamic_inputs['market_context']
        
        # Log dynamic calculation
        print(f"Dynamic Feed - Confidence: {signal_data['confidence']:.4f}")
        print(f"Dynamic Feed - Strength: {signal_data['strength']:.4f}")
        print(f"Dynamic Feed - Execution: {signal_data['execution_recommendation']}")
        print(f"Dynamic Feed - Accuracy: {math_data['accuracy_score']:.4f}")
        print(f"Dynamic Feed - Precision: {math_data['precision']:.6f}")
        
        # Get Agent Zero decision with dynamic inputs
        agent_zero_decision = agent_zero.predict(signal_data, math_data, full_market_context)
        
        # Add to analysis result
        analysis_result['agent_zero_decision'] = agent_zero_decision
        analysis_result['agent_zero_integrated'] = True
        analysis_result['dynamic_inputs'] = dynamic_inputs
        analysis_result['feed_type'] = 'dynamic'
        
        print(f"Agent Zero decision: {agent_zero_decision.get('action', 'unknown')}")
        print(f"Agent Zero confidence: {agent_zero_decision.get('confidence', 0):.1%}")
        
        # Shadow mode logging - capture training data with dynamic inputs
        try:
            agent_zero.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision=agent_zero_decision,
                outcome=0.0,  # Will be updated with actual performance
                market_context=full_market_context
            )
            print("Shadow mode: Dynamic training data logged")
        except Exception as e:
            print(f"Shadow mode logging failed: {e}")
        
        return analysis_result
        
    except Exception as e:
        print(f"Agent Zero integration error: {e}")
        analysis_result['agent_zero_error'] = str(e)
        analysis_result['agent_zero_integrated'] = False
        return analysis_result

def analyze_ticker(ticker: str, **kwargs) -> Dict[str, Any]:
    """Main analysis function with proper signal flow"""
    
    print(f"CORE Trading System Analysis: {ticker}")
    print("=" * 50)
    
    # Determine system to use
    system_type = get_trading_system()
    print(f"Selected system: {system_type}")
    
    # Run appropriate system
    if system_type == "ultimate":
        result = run_ultimate_orchestrator(ticker, **kwargs)
    elif system_type == "agent_orchestrator":
        result = run_agent_orchestrator(ticker, **kwargs)
    else:
        result = run_basic_system(ticker, **kwargs)
    
    # Integrate with Agent Zero if not already done
    if not result.get('agent_zero_decision') and result['status'] == 'SUCCESS':
        result = integrate_with_agent_zero(result)
    
    return result

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CORE Trading System - Ticker Agnostic')
    parser.add_argument('--ticker', default=None, help='Single ticker symbol to analyze')
    parser.add_argument('--tickers', nargs='+', help='Multiple ticker symbols (e.g. --tickers QQQ AAPL TSLA)')
    parser.add_argument('--system', choices=['ultimate', 'agent_orchestrator', 'basic'], 
                       help='Force specific system')
    parser.add_argument('--output', help='Output file for results')
    parser.add_argument('--batch', action='store_true', help='Run multi-ticker batch test')
    
    args = parser.parse_args()
    
    # Handle multi-ticker scenarios
    if args.batch or args.tickers:
        # Multi-ticker batch processing
        tickers = args.tickers or ['QQQ', 'AAPL', 'TSLA']  # Original test tickers
        
        print(f"MULTI-TICKER BATCH ANALYSIS")
        print(f"=" * 40)
        print(f"Tickers: {', '.join(tickers)}")
        
        batch_results = {}
        for ticker in tickers:
            print(f"\nProcessing {ticker}...")
            try:
                result = analyze_ticker(ticker)
                batch_results[ticker] = result
                print(f"  {ticker}: {result['status']}")
            except Exception as e:
                batch_results[ticker] = {'ticker': ticker, 'status': 'ERROR', 'error': str(e)}
                print(f"  {ticker}: ERROR - {e}")
        
        # Batch summary
        successful = sum(1 for r in batch_results.values() if r.get('status') == 'SUCCESS')
        print(f"\nBATCH COMPLETE: {successful}/{len(tickers)} successful")
        
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(batch_results, f, indent=2, default=str)
            print(f"Batch results saved to: {args.output}")
        
        return batch_results
    
    # Single ticker processing
    ticker = args.ticker or os.getenv('DEFAULT_TICKER', 'SPY')
    
    # Override system selection if specified
    if args.system:
        global get_trading_system
        def get_trading_system():
            return args.system
    
    # Run analysis
    try:
        result = analyze_ticker(ticker)
        
        # Print summary
        print(f"\nANALYSIS COMPLETE")
        print(f"=" * 30)
        print(f"Ticker: {result['ticker']}")
        print(f"System: {result['system']}")
        print(f"Status: {result['status']}")
        
        if result.get('agent_zero_decision'):
            az_decision = result['agent_zero_decision']
            print(f"Agent Zero Action: {az_decision.get('action', 'unknown')}")
            print(f"Agent Zero Confidence: {az_decision.get('confidence', 0):.1%}")
        
        if result.get('error'):
            print(f"Error: {result['error']}")
        
        # Save results if requested
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2, default=str)
            print(f"Results saved to: {args.output}")
        
        return result
        
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return {'error': str(e), 'status': 'CRITICAL_FAILURE'}

if __name__ == "__main__":
    main()
