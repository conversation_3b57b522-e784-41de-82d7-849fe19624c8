#!/usr/bin/env python3
"""
MSFT 60-Day Backtest Test
Test Agent Zero's trading signals against MSFT market data
"""

import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from agnostic_60_day_backtest import AgnosticBacktester, AgnosticBacktestConfig

def main():
    """Run MSFT 60-day backtest to validate Agent Zero signals"""
    print("MSFT 60-Day Backtest - Agent Zero Signal Validation")
    print("=" * 60)
    
    # Configure backtest
    config = AgnosticBacktestConfig(
        ticker='MSFT',
        days=60,
        initial_capital=100000,
        position_size_pct=0.1,  # 10% position sizing
        stop_loss_pct=0.02,     # 2% stop loss
        take_profit_pct=0.04,   # 4% take profit
        confidence_threshold=0.6 # 60% confidence threshold
    )
    
    print(f"Configuration:")
    print(f"  Ticker: {config.ticker}")
    print(f"  Days: {config.days}")
    print(f"  Initial Capital: ${config.initial_capital:,}")
    print(f"  Position Size: {config.position_size_pct:.1%}")
    print(f"  Stop Loss: {config.stop_loss_pct:.1%}")
    print(f"  Take Profit: {config.take_profit_pct:.1%}")
    print(f"  Confidence Threshold: {config.confidence_threshold:.1%}")
    print()
    
    try:
        # Initialize backtester
        print("Initializing backtester...")
        backtester = AgnosticBacktester(config)
        
        # Run backtest
        print("Running 60-day backtest...")
        results = backtester.run_backtest()
        
        # Display results
        print("\nBACKTEST RESULTS:")
        print("=" * 40)
        
        metrics = results['performance_metrics']
        print(f"Total Return: {metrics['total_return']:.2%}")
        print(f"Annualized Return: {metrics['annualized_return']:.2%}")
        print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
        print(f"Max Drawdown: {metrics['max_drawdown']:.2%}")
        print(f"Volatility: {metrics['volatility']:.2%}")
        print()
        
        print(f"Total Trades: {len(results['trades'])}")
        print(f"Win Rate: {metrics['win_rate']:.2%}")
        print(f"Average Trade Return: {metrics['avg_trade_return']:.2%}")
        print(f"Best Trade: {metrics['best_trade']:.2%}")
        print(f"Worst Trade: {metrics['worst_trade']:.2%}")
        print()
        
        # Agent Zero signal analysis
        agent_zero_decisions = results.get('agent_zero_decisions', [])
        print(f"Agent Zero Decisions: {len(agent_zero_decisions)}")
        
        if agent_zero_decisions:
            decisions_summary = {}
            for decision in agent_zero_decisions:
                final_decision = decision.get('final_decision', 'unknown')
                decisions_summary[final_decision] = decisions_summary.get(final_decision, 0) + 1
            
            print("Decision Distribution:")
            for decision, count in decisions_summary.items():
                print(f"  {decision}: {count}")
            
            # Average confidence
            confidences = [d.get('confidence', 0) for d in agent_zero_decisions if 'confidence' in d]
            if confidences:
                avg_confidence = sum(confidences) / len(confidences)
                print(f"Average Confidence: {avg_confidence:.1%}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"msft_backtest_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nDetailed results saved to: {results_file}")
        
        # Validation summary
        print("\nAGENT ZERO VALIDATION SUMMARY:")
        print("=" * 40)
        
        if metrics['total_return'] > 0:
            print("✓ Positive returns generated")
        else:
            print("✗ Negative returns")
            
        if metrics['sharpe_ratio'] > 1.0:
            print("✓ Good risk-adjusted returns (Sharpe > 1.0)")
        elif metrics['sharpe_ratio'] > 0.5:
            print("~ Moderate risk-adjusted returns (Sharpe > 0.5)")
        else:
            print("✗ Poor risk-adjusted returns (Sharpe < 0.5)")
            
        if metrics['win_rate'] > 0.5:
            print(f"✓ Positive win rate ({metrics['win_rate']:.1%})")
        else:
            print(f"✗ Low win rate ({metrics['win_rate']:.1%})")
            
        if len(results['trades']) > 0:
            print(f"✓ Generated {len(results['trades'])} trades")
        else:
            print("✗ No trades generated")
        
        return results
        
    except Exception as e:
        print(f"Error running backtest: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
