#!/usr/bin/env python3
"""
ULTIMATE ORCHESTRATOR: Complete Intelligent Trading System
B-Series + A-01 + C-02 + F-02 Flow Physics/CSID + SPECIALIZED AGENT ARMY Integration
CONSOLIDATED: Includes Agent Orchestrator capabilities for specialized agent coordination
"""

import os
import sys
import json
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import asyncio
import requests
from concurrent.futures import ThreadPoolExecutor

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from agents.anomaly_detector_agent import GreekAnomalyAgent
from agents.iv_dynamics_agent import IVDynamicsAgent
from agents.flow_physics_agent import FlowPhysicsAgent
from agents.enhanced_csid_agent import EnhancedCSIDAgent

# Import our enhanced Agent Zero Hub for maximum intelligence
from agents.agent_zero import get_agent_zero_hub

# Import enhanced accumulation distribution intelligence
try:
    from agents.accumulation_distribution_detector.enhanced_accumulation_distribution_agent import EnhancedAccumulationDistributionAgent
    def get_enhanced_accumulation_distribution_intelligence(ticker, price_data, volume_data, market_context):
        agent = EnhancedAccumulationDistributionAgent()
        return agent.analyze_accumulation_distribution(price_data, volume_data)
except ImportError:
    # Fallback if enhanced agent not available
    def get_enhanced_accumulation_distribution_intelligence(ticker, price_data, volume_data, market_context):
        return {
            'decision': 'NEUTRAL',
            'confidence': 50.0,
            'signal': {'strength': 0.5, 'direction': 'neutral'},
            'error': 'Enhanced agent not available'
        }

# ACTIVATION: Import Consolidated Options Intelligence Agent
from agents.agent_zero_options_agent import (
    AgentZeroOptionsAgent,
    EnhancedAgentZeroOptionsAgent,  # Backward compatibility alias
    create_enhanced_options_task
)
from options_intelligence_services import (
    OptionsIntelligenceService,
    OptionsDataContract,
    MarketContextContract
)

from agents.breakout_validation_specialist.breakout_validation_specialist import (
    BreakoutValidationAgent, BreakoutValidationConfig
)
from agents.options_flow_decoder.options_flow_decoder import (
    OptionsFlowDecoderAgent, OptionsFlowConfig
)

# CONSOLIDATED: Agent Orchestrator Capabilities Integration
from dataclasses import dataclass
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Specialized agent availability checks (from agent_orchestrator.py)
try:
    from agents.accumulation_distribution_detector.accumulation_distribution_detector import (
        AccumulationDistributionAgent, AccumulationDistributionConfig
    )
    ACCUMULATION_AGENT_AVAILABLE = True
except ImportError:
    ACCUMULATION_AGENT_AVAILABLE = False

# Note: BreakoutValidationAgent and OptionsFlowDecoderAgent already imported above
BREAKOUT_AGENT_AVAILABLE = True  # Already imported
OPTIONS_AGENT_AVAILABLE = True   # Already imported

@dataclass
class OrchestratorConfig:
    """Configuration for consolidated orchestrator capabilities"""
    timeout_ms: int = 15000
    schwab_mcp_url: str = "localhost:8005"
    enable_parallel_processing: bool = True
    confidence_threshold: float = 60.0
    ensemble_voting: bool = True
    real_time_mode: bool = True
    dynamic_feed_enabled: bool = True
    agent_zero_integration: bool = True

def ultimate_trading_pipeline(ticker):
    """
    Ultimate trading intelligence pipeline
    STEP 0: Single-Source Data Initialization
    B-Series  A-01  C-02  F-02  SPECIALIZED ARMY  Unified Intelligence for Agent Zero
    """
    print(f"ULTIMATE TRADING INTELLIGENCE: {ticker}")
    print("=" * 70)
    
    unified_analysis = {
        "ticker": ticker,
        "pipeline_components": ["DATA_INIT", "B-Series", "A-01", "C-02", "F-02", "SPECIALIZED_ARMY"],
        "timestamp": datetime.now().isoformat(),
        "intelligence_level": "MAXIMUM_WITH_SPECIALIZED_AGENTS"
    }
    
    # STEP 0: SINGLE-SOURCE DATA INITIALIZATION (Engineering Excellence)
    print("0: DATA INITIALIZATION: Single-source fetch from Schwab MCP...")
    from agents.data_ingestion_agent import LiveDataGatewayAgent
    from datetime import date
    import shutil
    
    try:
        # Initialize single-source data agent
        data_agent = LiveDataGatewayAgent()
        
        # Fetch from single source (localhost:8005)
        fetch_result = data_agent.execute([ticker], source="schwab")
        
        if fetch_result['status'] != 'OK':
            raise Exception(f"Single-source data fetch failed: {fetch_result}")
        
        # Ensure data consistency: Copy live data to history for pipeline
        today = date.today().isoformat()
        live_bars_path = Path(f"data/live/{today}/{ticker}_bars.parquet")
        live_opts_path = Path(f"data/live/{today}/{ticker}_options.parquet")
        history_bars_path = Path(f"data/history/{ticker}_bars.parquet")
        history_opts_path = Path(f"data/history/{ticker}_options.parquet")
        
        # Create history directory
        history_bars_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy to history for single-source consistency
        if live_bars_path.exists():
            shutil.copy2(live_bars_path, history_bars_path)
            print(f"   OK Bars data: {len(pd.read_parquet(history_bars_path))} records")
        else:
            raise Exception(f"Bars data not found: {live_bars_path}")
            
        if live_opts_path.exists():
            shutil.copy2(live_opts_path, history_opts_path)
            print(f"   OK Options data: {len(pd.read_parquet(history_opts_path))} records")
        else:
            raise Exception(f"Options data not found: {live_opts_path}")
        
        # Store data initialization result
        unified_analysis["data_initialization"] = {
            "status": "SUCCESS",
            "source": "schwab_mcp_localhost_8005",
            "bars_records": len(pd.read_parquet(history_bars_path)),
            "options_records": len(pd.read_parquet(history_opts_path)),
            "single_source_compliance": True,
            "data_timestamp": datetime.now().isoformat()
        }
        
        print(f"   OK Single-source data initialization complete (localhost:8005)")
        
    except Exception as e:
        print(f"   ERROR Data initialization failed: {e}")
        raise Exception(f"Single-source data initialization failed: {e}")
    
    # Step 1: B-Series Greek Feature Engineering
    print("1: B-Series: Building Greek features with ROC derivatives...")
    from tasks.build_features import FeatureBuilder
    
    builder = FeatureBuilder()
    feature_result = builder.run(ticker)
    
    if feature_result['status'] != 'SUCCESS':
        raise Exception(f"Feature building failed: {feature_result.get('error')}")
    
    # Extract latest feature row
    features_df = pd.read_parquet(feature_result['output_file'])
    
    if not features_df.empty:
        latest_features = features_df.iloc[-1].to_dict()
        feature_row = {k: (v.item() if hasattr(v, 'item') else v) for k, v in latest_features.items()}
    else:
        feature_row = {}
    
    unified_analysis["b_series"] = {
        "status": "SUCCESS",
        "feature_count": feature_result['feature_count'],
        "output_file": feature_result['output_file'],
        "greek_features": [col for col in features_df.columns if any(
            greek in col for greek in ['gamma', 'vanna', 'charm', 'delta', 'theta']
        )]
    }
    
    print(f"   Built {feature_result['feature_count']} features")
    
    # Step 2: A-01 Greek/IV Anomaly Detection
    print("2: A-01: Detecting Greek/IV statistical anomalies...")
    
    # Ensure historical stats exist
    from tasks.update_hist_stats import HistoricalStatsBuilder
    stats_builder = HistoricalStatsBuilder()
    
    if not os.path.exists("data/hist_stats.parquet"):
        print("   Building historical stats...")
        try:
            stats_result = stats_builder.run()
            if stats_result['status'] != 'SUCCESS':
                print(f"   Warning: Could not build stats: {stats_result.get('error')}")
                # Create minimal stats file for system continuity
                print("   Creating minimal stats file for system continuity...")
                minimal_stats = pd.DataFrame({
                    'feature': ['gamma_roc', 'delta_roc', 'vanna_roc'],
                    'mean': [0.0, 0.0, 0.0],
                    'std': [1.0, 1.0, 1.0],
                    'percentile_95': [1.96, 1.96, 1.96],
                    'percentile_99': [2.58, 2.58, 2.58]
                })
                os.makedirs("data", exist_ok=True)
                minimal_stats.to_parquet("data/hist_stats.parquet")
                print("   OK Minimal stats created for system continuity")
        except Exception as e:
            print(f"   Warning: Stats builder failed: {e}")
            # Create minimal stats file for system continuity
            print("   Creating minimal stats file for system continuity...")
            minimal_stats = pd.DataFrame({
                'feature': ['gamma_roc', 'delta_roc', 'vanna_roc'],
                'mean': [0.0, 0.0, 0.0],
                'std': [1.0, 1.0, 1.0],
                'percentile_95': [1.96, 1.96, 1.96],
                'percentile_99': [2.58, 2.58, 2.58]
            })
            os.makedirs("data", exist_ok=True)
            minimal_stats.to_parquet("data/hist_stats.parquet")
            print("   OK Minimal stats created for system continuity")
    
    # Run anomaly detection
    anomaly_agent = GreekAnomalyAgent()
    anomaly_file = anomaly_agent.execute(feature_row, ticker)
    
    # Load anomaly results
    with open(anomaly_file, 'r') as f:
        anomaly_data = json.load(f)
    
    unified_analysis["a01_anomalies"] = anomaly_data
    
    print(f"    Detected {len(anomaly_data.get('anomalies', []))} anomalies")
    
    # Step 2.5: F-01 Enhanced CSID Analysis
    print("2.5: F-01: Enhanced CSID institutional flow detection...")
    
    try:
        csid_agent = EnhancedCSIDAgent()
        
        # Use the same price data path as feature builder
        # FIX #2: Standardized price path
        # Use consistent single-source price data path
        price_parquet_path = f"data/history/{ticker}_bars.parquet"
        
        # Ensure path exists from data initialization
        if Path(price_parquet_path).exists():
            csid_path = csid_agent.execute(ticker, price_parquet_path)
            
            # Load CSID results
            with open(csid_path, 'r') as f:
                csid_data = json.load(f)
            
            unified_analysis["f01_csid"] = csid_data
            unified_analysis["csid_file"] = csid_path
            
            # Extract key metrics with error handling
            smart_money_index = csid_data.get('smart_money_index', 0.0)
            institutional_bias = csid_data.get('institutional_bias', 0.5)
            flow_regime = csid_data.get('flow_regime', 'mixed')
            csid_signals = csid_data.get('csid_signals', [])
            
            # Convert to float with error handling
            try:
                smart_money_val = float(smart_money_index) if smart_money_index != 'error' else 0.0
                institutional_bias_val = float(institutional_bias) if institutional_bias != 'error' else 0.5
            except (ValueError, TypeError):
                smart_money_val = 0.0
                institutional_bias_val = 0.5
            
            print(f"    Smart Money Index: {smart_money_val:.3f}")
            print(f"    Institutional Bias: {institutional_bias_val:.3f}")
            print(f"    Flow Regime: {flow_regime}")
            print(f"    CSID Signals: {len(csid_signals)}")
            
        else:
            print("    Price data not available for CSID analysis")
            unified_analysis["f01_csid"] = {"error": "Price data not available"}
            
    except Exception as e:
        print(f"    Enhanced CSID analysis failed: {e}")
        unified_analysis["f01_csid"] = {"error": str(e)}
    
    # Step 3: C-02 IV Dynamics & ROC Analysis
    print("3: C-02: Analyzing IV ROC dynamics & regime detection...")
    
    try:
        iv_agent = IVDynamicsAgent()
        iv_result = iv_agent.execute(ticker)
        
        # Handle both string error responses and dict success responses
        if isinstance(iv_result, dict) and iv_result.get('status') == 'SUCCESS':
            # Load IV dynamics results
            with open(iv_result['iv_dynamics_file'], 'r') as f:
                iv_dynamics_data = json.load(f)
            
            with open(iv_result['regime_file'], 'r') as f:
                regime_data = json.load(f)
            
            unified_analysis["c02_iv_dynamics"] = iv_dynamics_data
            unified_analysis["c02_regime"] = regime_data
            
            roc_signals = iv_dynamics_data.get("iv_roc_analysis", {}).get("roc_signals", [])
            regime_detected = iv_dynamics_data.get("regime_analysis", {}).get("regime_shift_detected", False)
            
            print(f"   IV ROC signals: {len(roc_signals)}, Regime shift: {regime_detected}")
        else:
            # Handle error case
            error_msg = iv_result if isinstance(iv_result, str) else str(iv_result)
            print(f"   IV dynamics analysis failed: {error_msg}")
            unified_analysis["c02_iv_dynamics"] = {"error": error_msg}
            unified_analysis["c02_regime"] = {"error": error_msg}
            
    except Exception as e:
        print(f"   IV dynamics analysis error: {e}")
        unified_analysis["c02_iv_dynamics"] = {"error": str(e)}
        unified_analysis["c02_regime"] = {"error": str(e)}
    
    # Step 4: F-02 Flow Physics & CSID Final Analysis
    print("4: F-02: Flow physics modeling & final CSID calculations...")
    
    try:
        flow_agent = FlowPhysicsAgent()
        
        # Use the same price data path (single-source consistency)
        price_parquet_path = f"data/history/{ticker}_bars.parquet"
        flow_result = flow_agent.execute(ticker, price_parquet_path)
        
        # Handle both string error responses and dict success responses
        if isinstance(flow_result, dict) and flow_result.get('status') == 'SUCCESS':
            with open(flow_result['output_file'], 'r') as f:
                flow_data = json.load(f)
            
            unified_analysis["f02_flow_physics"] = flow_data
            
            flow_score = flow_data.get("flow_score", 0.0)
            flow_direction = flow_data.get("flow_direction", "neutral")
            
            try:
                flow_score_val = float(flow_score) if flow_score != 'error' else 0.0
            except (ValueError, TypeError):
                flow_score_val = 0.0
            
            print(f"   Flow score: {flow_score_val:.3f}, Direction: {flow_direction}")
        else:
            # Handle error case
            error_msg = flow_result if isinstance(flow_result, str) else str(flow_result)
            print(f"   Flow physics analysis failed: {error_msg}")
            unified_analysis["f02_flow_physics"] = {"error": error_msg}
            
    except Exception as e:
        print(f"   Flow physics analysis error: {e}")
        unified_analysis["f02_flow_physics"] = {"error": str(e)}
    
    # Step 5: SPECIALIZED AGENT ARMY - The Masters
    print("5: SPECIALIZED ARMY: Deploying orchestrated masters...")
    
    try:
        # Initialize orchestrator configuration
        config = OrchestratorConfig()
        
        # Fetch real market data (no synthetic fallbacks)
        print("    Fetching real market data from Schwab MCP...")
        try:
            market_data = _fetch_real_market_data_for_orchestrator(ticker)
            try:
                price_val = float(market_data['price']) if market_data.get('price') and market_data['price'] != 'error' else 0.0
            except (ValueError, TypeError, KeyError):
                price_val = 0.0
            
            print(f"    Real-time data acquired: ${price_val:.2f}")
        except Exception as e:
            print(f"    Real market data fetch failed: {e}")
            unified_analysis["specialized_army"] = {"error": "Real market data unavailable"}
            raise Exception("Real market data required for orchestrated execution")
        
        # Execute specialized agents with orchestrator coordination
        # Deploy ENHANCED AccumulationDistribution Master for Agent Zero
        print("    Deploying ENHANCED AccumulationDistribution Master (Agent Zero Ready)...")

        # Create market context from previous analysis stages
        market_context = {
            'b_series_analysis': unified_analysis.get('b_series', {}),
            'anomaly_analysis': unified_analysis.get('a01_anomalies', {}),
            'iv_dynamics_analysis': unified_analysis.get('c02_iv_dynamics', {}),
            'flow_analysis': unified_analysis.get('f02_flow_physics', {})
        }

        # Get market data for agents
        price_data, volume_data = _extract_market_data_for_agents(ticker, unified_analysis)

        if price_data is not None and volume_data is not None:
            # Get enhanced institutional-grade analysis for Agent Zero
            enhanced_acc_result = get_enhanced_accumulation_distribution_intelligence(
                ticker, price_data, volume_data, market_context
            )

            unified_analysis["specialized_army"] = {
                "enhanced_accumulation_distribution": enhanced_acc_result
            }
            
            # Extract key metrics for display
            signal = enhanced_acc_result.get('signal', {})
            institutional_flow = enhanced_acc_result.get('institutional_flow', {})
            market_intelligence = enhanced_acc_result.get('market_intelligence', {})
            
            print(f"       ENHANCED Analysis:")
            print(f"         Signal: {signal.get('direction', 'neutral').upper()} (Strength: {float(signal.get('strength', 0)):.2f})")
            print(f"         Confidence: {float(signal.get('confidence', 0)):.2f} | Tier: {signal.get('tier', 'UNKNOWN')}")
            print(f"         Institutional Flow: {float(institutional_flow.get('flow_direction', 0)):+.2f}")
            print(f"         Market Regime: {market_intelligence.get('volatility_regime', 'UNKNOWN')}")
            print(f"         Agent Zero Ready: ")
            
            # Deploy BreakoutValidation Specialist
            print("    Deploying BreakoutValidation Specialist...")
            breakout_agent = BreakoutValidationAgent(BreakoutValidationConfig())
            
            # Calculate support/resistance from price data
            resistance_level = float(max(price_data[-20:]))  # 20-period high
            support_level = float(min(price_data[-20:]))     # 20-period low
            current_price = float(price_data[-1])
            direction = 'up' if current_price > resistance_level * 0.99 else 'down'
            
            breakout_result = breakout_agent.process({
                'price_data': price_data,
                'volume_data': volume_data,
                'resistance_level': resistance_level,
                'support_level': support_level,
                'symbol': ticker,
                'breakout_direction': direction
            })
            
            unified_analysis["specialized_army"]["breakout_validation"] = breakout_result
            
            print(f"      Breakout: {float(breakout_result['breakout_validity']):.1f}% valid ({breakout_result['recommendation']})")
            
            # Deploy OptionsFlow Expert (if options data available)
            print("    Deploying OptionsFlow Expert...")
            try:
                options_data = _get_options_data_for_agent(ticker)
                if options_data:
                    options_agent = OptionsFlowDecoderAgent(OptionsFlowConfig())
                    
                    options_result = options_agent.process({
                        'options_chain': options_data.get('options_chain', {}),
                        'underlying_price': current_price,
                        'iv_data': options_data.get('iv_data', {}),
                        'volume_data': options_data.get('volume_data', {}),
                        'symbol': ticker,
                        'expiration_dates': list(options_data.get('options_chain', {}).keys())
                    })
                    
                    unified_analysis["specialized_army"]["options_flow"] = options_result
                    
                    print(f"      Options: {options_result['directional_bias']} {options_result['bias_strength']} bias")
                    
                else:
                    print("      Options data not available")
                    unified_analysis["specialized_army"]["options_flow"] = {"error": "Options data not available"}
                    
            except Exception as e:
                print(f"       Options analysis failed: {e}")
                unified_analysis["specialized_army"]["options_flow"] = {"error": str(e)}
            
            # PHASE 2.7: OPTIONS INTELLIGENCE AGENT - CONTINUOUS LEARNING
            print("2.7: OPTIONS INTELLIGENCE: Advanced options analysis with learning...")
            
            try:
                # Initialize Options Intelligence Agent with learning capabilities
                options_agent = EnhancedAgentZeroOptionsAgent(
                    agent_id="options_intelligence_continuous_learning"
                )
                
                # Extract market context from previous analyses (Agent Zero runs after all agents)
                market_context = MarketContextContract(
                    iv_rank=unified_analysis.get('c02_iv_dynamics', {}).get('iv_rank', 50.0),
                    iv_trend=unified_analysis.get('c02_iv_dynamics', {}).get('iv_trend', 'stable'),
                    price_trend='neutral',  # Will be updated after Agent Zero runs
                    volatility_regime=unified_analysis.get('c02_iv_dynamics', {}).get('volatility_regime', 'normal_vol'),
                    historical_vol_20d=unified_analysis.get('c02_iv_dynamics', {}).get('historical_vol_20d', 0.20)
                )

                # Create options analysis task with placeholder signal (Agent Zero runs after all agents)
                placeholder_signal = {'action': 'neutral', 'confidence': 0.5}
                options_task = create_enhanced_options_task(
                    ticker=ticker,
                    market_context=market_context,
                    agent_zero_signal=placeholder_signal
                )
                
                # Execute options analysis with learning feedback loop
                options_result = options_agent.execute_task(options_task)
                
                # Store results and update continuous learning
                unified_analysis["options_intelligence"] = {
                    "status": options_result.status.value,
                    "trade_analysis": options_result.result.get('trade_analysis', {}),
                    "position_sizing": options_result.result.get('position_sizing', {}),
                    "exit_strategy": options_result.result.get('exit_strategy', {}),
                    "learning_metrics": options_result.result.get('learning_metrics', {}),
                    "roi_validation": options_result.result.get('roi_validation', False),
                    "execution_time_ms": options_result.execution_time_ms
                }
                
                print(f"       Trade Classification: {options_result.result.get('trade_analysis', {}).get('trade_classification', 'N/A')}")
                print(f"       Position Size: {options_result.result.get('position_sizing', {}).get('recommended_size', 0)} contracts")
                print(f"       ROI Valid: {options_result.result.get('roi_validation', False)}")
                print(f"       Learning Score: {float(options_result.result.get('learning_metrics', {}).get('confidence_improvement', 0)):.3f}")
                
            except Exception as e:
                print(f"       Options Intelligence analysis failed: {e}")
                unified_analysis["options_intelligence"] = {"error": str(e)}
            
            # Specialized army analysis complete - Agent Zero will be called after all agents
            
        else:
            print("    Market data not available for specialized agents")
            unified_analysis["specialized_army"] = {"error": "Market data not available"}
            
    except Exception as e:
        print(f"    Specialized army deployment failed: {e}")
        unified_analysis["specialized_army"] = {"error": str(e)}

    # AGENT ZERO FINAL DECISION - Called AFTER all agents complete
    print("\n6: AGENT ZERO: Final ensemble decision making...")
    try:
        agent_zero_hub = get_agent_zero_hub()

        # Generate real ensemble intelligence from all agents
        ensemble_intelligence = _generate_ensemble_intelligence(unified_analysis)

        # Extract real signal data from ensemble
        signal_data = {
            'confidence': ensemble_intelligence['confidence'] / 100,  # Convert to 0-1
            'strength': ensemble_intelligence['ensemble_score'] / 100,  # Convert to 0-1
            'execution_recommendation': 'execute' if ensemble_intelligence['confidence'] > 70 else 'hold',
            'final_decision': ensemble_intelligence['final_decision'],
            'component_signals': ensemble_intelligence['component_signals'],
            'component_weights': ensemble_intelligence['component_weights']
        }

        math_data = {
            'accuracy_score': min(ensemble_intelligence['confidence'] / 100, 1.0),
            'precision': 0.001,
            'ensemble_score': ensemble_intelligence['ensemble_score'],
            'intelligence_sources': ensemble_intelligence['intelligence_sources']
        }

        # Enhanced prediction with REAL ensemble intelligence
        agent_zero_intelligence = agent_zero_hub.predict(
            signal_data=signal_data,
            math_data=math_data,
            market_context={
                'b_series_analysis': unified_analysis.get('b_series', {}),
                'anomaly_analysis': unified_analysis.get('a01_anomalies', {}),
                'iv_dynamics_analysis': unified_analysis.get('c02_iv_dynamics', {}),
                'flow_analysis': unified_analysis.get('f02_flow_physics', {}),
                'specialized_intelligence': unified_analysis.get('specialized_army', {}),
                'ensemble_intelligence': ensemble_intelligence,  # Add ensemble data
                'ticker': ticker
            }
        )

        unified_analysis["agent_zero_intelligence"] = agent_zero_intelligence
        unified_analysis["ensemble_intelligence"] = ensemble_intelligence

        print(f"    ENSEMBLE INTELLIGENCE SUMMARY:")
        print(f"       Ensemble Score: {ensemble_intelligence['ensemble_score']:.1f}%")
        print(f"       Ensemble Decision: {ensemble_intelligence['final_decision']}")
        print(f"       Ensemble Confidence: {ensemble_intelligence['confidence']:.1f}%")
        print(f"       Active Signals: {len(ensemble_intelligence['intelligence_sources'])}")
        print(f"    AGENT ZERO FINAL DECISION:")
        print(f"       Final Decision: {agent_zero_intelligence.get('action', 'UNKNOWN')}")
        print(f"       Confidence: {agent_zero_intelligence.get('confidence', 0):.1%}")
        print(f"       Enhanced Features: {agent_zero_intelligence.get('enhanced', False)}")
        print(f"       ML Powered: {agent_zero_intelligence.get('ml_available', False)}")
        print(f"       Decision Method: {agent_zero_intelligence.get('decision_method', 'unknown')}")

    except Exception as e:
        print(f"    Agent Zero final decision failed: {e}")
        unified_analysis["agent_zero_intelligence"] = {"error": str(e)}

    # Final: Complete Intelligence Package
    print("\n ULTIMATE INTELLIGENCE COMPLETE")
    print("=" * 70)
    
    # Save unified analysis
    output_file = f"outputs/ultimate_intelligence_{ticker}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    os.makedirs("outputs", exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(unified_analysis, f, indent=2, default=str)
    
    print(f" Complete intelligence saved: {output_file}")
    
    return unified_analysis

# CONSOLIDATED ORCHESTRATOR FUNCTIONS (from agent_orchestrator.py)
async def _execute_specialized_agents_orchestrated(ticker: str, market_data: Dict[str, Any], config: OrchestratorConfig) -> Dict[str, Any]:
    """Execute specialized agents with orchestrator coordination"""
    results = {}
    
    # Initialize available agents
    agents = {}
    
    if ACCUMULATION_AGENT_AVAILABLE:
        agents['accumulation_distribution'] = AccumulationDistributionAgent(AccumulationDistributionConfig())
        
    if BREAKOUT_AGENT_AVAILABLE:
        agents['breakout_validation'] = BreakoutValidationAgent(BreakoutValidationConfig())
        
    if OPTIONS_AGENT_AVAILABLE:
        agents['options_flow'] = OptionsFlowDecoderAgent(OptionsFlowConfig())
    
    if not agents:
        return {'error': 'No specialized agents available'}
    
    # Execute agents in parallel if enabled
    if config.enable_parallel_processing:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            agent_tasks = []
            for agent_name, agent in agents.items():
                task_data = {
                    'symbol': ticker,
                    'market_data': market_data,
                    'analysis_type': agent_name,
                    'real_time_mode': config.real_time_mode
                }
                # Convert to async task
                agent_tasks.append(_run_agent_safely(agent_name, agent, task_data))
            
            # Execute all tasks concurrently
            agent_results = await asyncio.gather(*agent_tasks, return_exceptions=True)
            
            # Process results
            for i, (agent_name, result) in enumerate(zip(agents.keys(), agent_results)):
                if isinstance(result, Exception):
                    results[agent_name] = {'error': str(result), 'status': 'FAILED'}
                else:
                    results[agent_name] = result
                    
        finally:
            loop.close()
    else:
        # Sequential execution
        for agent_name, agent in agents.items():
            try:
                task_data = {
                    'symbol': ticker,
                    'market_data': market_data,
                    'analysis_type': agent_name,
                    'real_time_mode': config.real_time_mode
                }
                result = await _run_agent_safely(agent_name, agent, task_data)
                results[agent_name] = result
            except Exception as e:
                results[agent_name] = {'error': str(e), 'status': 'FAILED'}
    
    # Apply ensemble voting if enabled
    if config.ensemble_voting and len(results) > 1:
        ensemble_decision = _apply_ensemble_voting(results, config.confidence_threshold)
        results['ensemble_decision'] = ensemble_decision
    
    return results

async def _run_agent_safely(agent_name: str, agent, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """Run agent with error handling and timeout"""
    try:
        # Create appropriate task for the agent
        if hasattr(agent, 'analyze'):
            result = agent.analyze(task_data)
        elif hasattr(agent, 'process'):
            result = await agent.process(task_data)
        elif hasattr(agent, 'execute'):
            result = agent.execute(task_data['symbol'])
        else:
            return {'error': f'Agent {agent_name} has no recognized execution method', 'status': 'FAILED'}
        
        return {
            'agent': agent_name,
            'result': result,
            'status': 'SUCCESS',
            'timestamp': datetime.now().isoformat()
        }
    except Exception as e:
        return {
            'agent': agent_name,
            'error': str(e),
            'status': 'FAILED',
            'timestamp': datetime.now().isoformat()
        }

def _apply_ensemble_voting(agent_results: Dict[str, Any], confidence_threshold: float) -> Dict[str, Any]:
    """Apply ensemble voting across agent results"""
    votes = []
    confidences = []
    
    for agent_name, result in agent_results.items():
        if isinstance(result, dict) and result.get('status') == 'SUCCESS':
            agent_result = result.get('result', {})
            
            # Extract signal/decision
            signal = None
            confidence = 0.0
            
            if 'signal' in agent_result:
                signal = agent_result['signal']
            elif 'action' in agent_result:
                signal = agent_result['action']
            elif 'decision' in agent_result:
                signal = agent_result['decision']
            
            if 'confidence' in agent_result:
                confidence = float(agent_result['confidence'])
            elif 'score' in agent_result:
                confidence = float(agent_result['score'])
            
            if signal and confidence > 0:
                votes.append(signal)
                confidences.append(confidence)
    
    if not votes:
        return {'decision': 'HOLD', 'confidence': 0.0, 'method': 'no_valid_votes'}
    
    # Simple majority voting with confidence weighting
    avg_confidence = sum(confidences) / len(confidences)
    
    # Count votes
    vote_counts = {}
    for vote in votes:
        vote_counts[vote] = vote_counts.get(vote, 0) + 1
    
    # Get majority decision
    majority_decision = max(vote_counts, key=vote_counts.get)
    majority_strength = vote_counts[majority_decision] / len(votes)
    
    final_confidence = avg_confidence * majority_strength
    
    return {
        'decision': majority_decision,
        'confidence': final_confidence,
        'vote_distribution': vote_counts,
        'agent_count': len(votes),
        'majority_strength': majority_strength,
        'method': 'ensemble_voting'
    }

def _fetch_real_market_data_for_orchestrator(ticker: str) -> Dict[str, Any]:
    """Fetch real market data with no synthetic fallbacks"""
    try:
        import requests
        response = requests.get(f"http://localhost:8005/quotes/{ticker}", timeout=10)
        if response.status_code == 200:
            data = response.json()
            return {
                'price': data.get('last', 0),
                'bid': data.get('bid', 0),
                'ask': data.get('ask', 0),
                'volume': data.get('volume', 0),
                'timestamp': datetime.now().isoformat(),
                'source': 'schwab_mcp'
            }
        else:
            raise Exception(f"MCP server returned {response.status_code}")
    except Exception as e:
        # NO SYNTHETIC FALLBACK - enforce real data only
        raise Exception(f"Real market data fetch failed: {e}")

def _extract_market_data_for_agents(ticker, feature_result):
    """Extract price and volume data for specialized agents"""
    try:
        # Get price data from Schwab MCP or feature builder output
        # FIX #2: Standardized price path
        # Use consistent single-source price data path
        price_parquet_path = f"data/history/{ticker}_bars.parquet"
        
        # Ensure path exists from data initialization
        if price_parquet_path and Path(price_parquet_path).exists():
            price_df = pd.read_parquet(price_parquet_path)
            
            if 'close' in price_df.columns and 'volume' in price_df.columns:
                # Use last 50 periods for analysis
                price_data = price_df['close'].tail(50).values
                volume_data = price_df['volume'].tail(50).values
                
                return price_data, volume_data
        
        # Fallback: Try to get data from Schwab MCP directly
        from api.modules.schwab_production_api import SchwabProductionAPI
        
        schwab = SchwabProductionAPI()
        bars_data = schwab.get_price_history(ticker, period_type='day', period=50)
        
        if bars_data and 'candles' in bars_data:
            candles = bars_data['candles']
            price_data = [candle['close'] for candle in candles]
            volume_data = [candle['volume'] for candle in candles]
            
            return np.array(price_data), np.array(volume_data)
        
        return None, None
        
    except Exception as e:
        print(f"    Could not extract market data: {e}")
        return None, None

def _get_options_data_for_agent(ticker):
    """Get options data for OptionsFlow agent"""
    try:
        from api.modules.schwab_production_api import SchwabProductionAPI
        
        schwab = SchwabProductionAPI()
        options_chain = schwab.get_option_chain(ticker)
        
        if options_chain:
            return {
                'options_chain': options_chain,
                'iv_data': {},  # Would extract from chain
                'volume_data': {}  # Would extract from chain
            }
        
        return None
        
    except Exception as e:
        print(f"    Could not get options data: {e}")
        return None

def _generate_ensemble_intelligence(unified_analysis):
    """Generate final ensemble intelligence for Agent Zero"""
    
    # Extract signals from all components
    signals = {
        'b_series': 0.5,       # Neutral default
        'anomalies': 0.5,      # Neutral default
        'csid': 0.5,           # Neutral default
        'iv_dynamics': 0.5,    # Neutral default
        'flow_physics': 0.5,   # Neutral default
        'accumulation': 0.5,   # Neutral default
        'breakout': 0.5,       # Neutral default
        'options_flow': 0.5    # Neutral default
    }
    
    # Extract CSID signal
    csid_data = unified_analysis.get('f01_csid', {})
    if 'institutional_bias' in csid_data:
        bias = csid_data['institutional_bias']
        # Handle error cases where bias is a string
        if isinstance(bias, (int, float)):
            signals['csid'] = bias
        elif bias == 'bullish':
            signals['csid'] = 0.7
        elif bias == 'bearish':
            signals['csid'] = 0.3
        else:  # 'error', 'neutral', or other strings
            signals['csid'] = 0.5
    
    # Extract Flow Physics signal
    flow_data = unified_analysis.get('f02_flow_physics', {})
    if 'flow_score' in flow_data:
        # Convert flow score to 0-1 probability
        flow_score = flow_data['flow_score']
        signals['flow_physics'] = max(0, min(1, (flow_score + 1) / 2))
    
    # Extract Specialized Army signals
    army_data = unified_analysis.get('specialized_army', {})
    
    # Handle ENHANCED Accumulation/Distribution Agent output for Agent Zero
    if 'enhanced_accumulation_distribution' in army_data:
        enhanced_acc_data = army_data['enhanced_accumulation_distribution']
        signal_data = enhanced_acc_data.get('signal', {})
        
        # Extract sophisticated signal strength (already 0-1 normalized)
        signal_strength = signal_data.get('strength', 0.5)
        signal_direction = signal_data.get('direction', 'neutral')
        
        # Convert direction to probability
        if signal_direction == 'bullish':
            signals['accumulation'] = 0.5 + (signal_strength / 2)  # 0.5 to 1.0
        elif signal_direction == 'bearish': 
            signals['accumulation'] = 0.5 - (signal_strength / 2)  # 0.0 to 0.5
        else:
            signals['accumulation'] = 0.5  # Neutral
    elif 'accumulation_distribution' in army_data:
        # Fallback to basic agent if enhanced not available
        acc_prob = army_data['accumulation_distribution'].get('accumulation_probability', 50)
        signals['accumulation'] = acc_prob / 100
    
    if 'breakout_validation' in army_data:
        breakout_validity = army_data['breakout_validation'].get('breakout_validity', 50)
        signals['breakout'] = breakout_validity / 100
    
    if 'options_flow' in army_data and 'error' not in army_data['options_flow']:
        bias = army_data['options_flow'].get('directional_bias', 'NEUTRAL')
        if bias == 'BULLISH':
            signals['options_flow'] = 0.7
        elif bias == 'BEARISH':
            signals['options_flow'] = 0.3
    
    # Weighted ensemble for Agent Zero (Enhanced accumulation gets higher weight)
    weights = {
        'accumulation': 0.30,      # ENHANCED agent gets increased weight (was 0.25)
        'breakout': 0.18,          # Slightly reduced to accommodate enhanced agent
        'options_flow': 0.15,      # Specialized army
        'csid': 0.15,              # Institutional flow
        'flow_physics': 0.15,      # Flow physics
        'anomalies': 0.05,         # Anomaly detection
        'iv_dynamics': 0.05        # IV dynamics
    }
    
    # Calculate weighted ensemble score
    ensemble_score = sum(signals[key] * weights.get(key, 0) for key in signals.keys())
    confidence = 1 - abs(ensemble_score - 0.5) * 2  # Distance from neutral
    
    # Generate final decision for Agent Zero
    if ensemble_score > 0.65:
        final_decision = "BULLISH"
        strength = "STRONG" if ensemble_score > 0.75 else "MODERATE"
    elif ensemble_score < 0.35:
        final_decision = "BEARISH"
        strength = "STRONG" if ensemble_score < 0.25 else "MODERATE"
    else:
        final_decision = "NEUTRAL"
        strength = "WEAK"
    
    # Agent Zero recommendation
    if confidence > 0.7:
        if final_decision == "BULLISH":
            agent_zero_rec = f"EXECUTE BUY - {strength} bullish confluence"
        elif final_decision == "BEARISH":
            agent_zero_rec = f"EXECUTE SELL - {strength} bearish confluence"
        else:
            agent_zero_rec = "HOLD - Neutral confluence"
    else:
        agent_zero_rec = "WAIT - Low confidence confluence"
    
    return {
        'final_decision': final_decision,
        'strength': strength,
        'confidence': confidence * 100,
        'ensemble_score': ensemble_score * 100,
        'agent_zero_recommendation': agent_zero_rec,
        'component_signals': signals,
        'component_weights': weights,
        'intelligence_sources': list(signals.keys())
    }

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python ultimate_orchestrator.py <TICKER>")
        sys.exit(1)
    
    ticker = sys.argv[1].upper()
    
    try:
        result = ultimate_trading_pipeline(ticker)
        print(f"\n Ultimate intelligence pipeline completed for {ticker}")
        
        # Display Agent Zero enhanced intelligence summary
        agent_zero_intel = result.get('agent_zero_intelligence', {})
        if agent_zero_intel:
            print(f"\n AGENT ZERO ENHANCED INTELLIGENCE SUMMARY:")
            print(f"   Action: {agent_zero_intel.get('action', 'UNKNOWN')}")
            print(f"   Confidence: {agent_zero_intel.get('confidence', 0):.1%}")
            print(f"   Enhanced: {agent_zero_intel.get('enhanced', False)}")
            print(f"   ML Powered: {agent_zero_intel.get('ml_available', False)}")
            print(f"   Decision Method: {agent_zero_intel.get('decision_method', 'unknown')}")
            
            # Show advanced capabilities if available
            if agent_zero_intel.get('enhanced'):
                advanced = agent_zero_intel.get('advanced_capabilities', {})
                print(f"    Advanced Features:")
                print(f"      - Risk Adaptation: {advanced.get('risk_adaptation', False)}")
                print(f"      - Regime Prediction: {advanced.get('regime_prediction', False)}")
                print(f"      - Multi-Timeframe: {advanced.get('multi_timeframe', False)}")
                print(f"      - Sentiment Analysis: {advanced.get('sentiment_analysis', False)}")
                print(f"      - Auto Optimization: {advanced.get('auto_optimization', False)}")
            
            # Show reasoning
            reasoning = agent_zero_intel.get('reasoning', [])
            if reasoning:
                print(f"    Reasoning:")
                for i, reason in enumerate(reasoning[:3], 1):  # Show top 3 reasons
                    print(f"      {i}. {reason}")
                    
            # Shadow mode logging - capture ultimate orchestrator decisions with dynamic feeds
            try:
                from agents.agent_zero import AgentZeroAdvisor
                from dynamic_feed_calculator import DynamicFeedCalculator
                
                shadow_agent = AgentZeroAdvisor()
                
                # Extract comprehensive analysis for dynamic calculation
                comprehensive_signals = result.get('comprehensive_analysis', {})
                
                # Create market context from ultimate orchestrator analysis
                market_context = {
                    'b_series_analysis': {
                        'features': comprehensive_signals.get('b_series_features', {}),
                        'confidence': comprehensive_signals.get('b_series_confidence', 0.6),
                        'pattern_strength': comprehensive_signals.get('pattern_strength', 0.5)
                    },
                    'flow_analysis': {
                        'momentum': comprehensive_signals.get('flow_momentum', 0.0),
                        'direction': comprehensive_signals.get('flow_direction', 'neutral'),
                        'strength': comprehensive_signals.get('flow_strength', 0.5)
                    },
                    'anomaly_analysis': {
                        'anomaly_detected': comprehensive_signals.get('anomaly_detected', False),
                        'anomaly_score': comprehensive_signals.get('anomaly_score', 0.0)
                    },
                    'iv_dynamics_analysis': {
                        'iv_rank': comprehensive_signals.get('iv_rank', 50.0),
                        'iv_expansion': comprehensive_signals.get('iv_expansion', False),
                        'volatility_regime': comprehensive_signals.get('volatility_regime', 'normal')
                    },
                    'market_regime': {
                        'trend': comprehensive_signals.get('trend', 'sideways'),
                        'volatility': comprehensive_signals.get('volatility', 'medium')
                    },
                    'system': 'ultimate_orchestrator',
                    'ticker': ticker,
                    'intelligence_sources': comprehensive_signals.get('intelligence_sources', []),
                    'component_signals': comprehensive_signals.get('component_signals', {})
                }
                
                # Use dynamic feed calculator
                calculator = DynamicFeedCalculator()
                dynamic_inputs = calculator.calculate_all_dynamic_inputs(market_context)
                
                # Extract dynamic data
                signal_data = dynamic_inputs['signal_data']
                math_data = dynamic_inputs['math_data']
                full_market_context = dynamic_inputs['market_context']
                
                print(f"Ultimate Orchestrator Dynamic Feed:")
                print(f"  Confidence: {float(signal_data['confidence']):.4f}")
                print(f"  Execution: {signal_data['execution_recommendation']}")
                
                shadow_agent.log_training_data(
                    signal_data=signal_data,
                    math_data=math_data, 
                    decision=agent_zero_intel,
                    outcome=0.0,  # Will be updated with performance
                    market_context=full_market_context
                )
                print("Shadow mode: Ultimate orchestrator dynamic data logged")
                
            except Exception as shadow_error:
                print(f"Shadow mode logging failed: {shadow_error}")
        else:
            print(f"\n  Agent Zero intelligence not available")
            
    except Exception as e:
        print(f" Pipeline failed: {e}")
        import traceback
        traceback.print_exc()


# CONSOLIDATED: Agent Orchestrator Compatibility Mode
def run_agent_orchestrator_mode(ticker: str, **kwargs) -> Dict[str, Any]:
    """
    Compatibility function for agent orchestrator mode
    Uses the full ultimate_trading_pipeline but extracts orchestrated results
    """
    try:
        # Run the full ultimate pipeline
        full_result = ultimate_trading_pipeline(ticker)
        
        # Extract the specialized army results
        specialized_army = full_result.get('specialized_army', {})
        
        # Create orchestrator-compatible response
        return {
            'system': 'consolidated_orchestrator',
            'ticker': ticker,
            'timestamp': datetime.now().isoformat(),
            'orchestrated_results': specialized_army,
            'ensemble_decision': specialized_army.get('enhanced_accumulation_distribution', {}),
            'signal_sources': ['enhanced_accumulation_distribution', 'breakout_validation', 'options_flow'],
            'status': 'SUCCESS'
        }
        
    except Exception as e:
        return {
            'system': 'consolidated_orchestrator',
            'ticker': ticker,
            'error': str(e),
            'status': 'FAILED',
            'timestamp': datetime.now().isoformat()
        }
